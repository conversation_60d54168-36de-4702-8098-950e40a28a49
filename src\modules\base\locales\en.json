{"删除": "Delete", "新增成员": "Add Member", "目录": "Directory", "菜单": "<PERSON><PERSON>", "权限": "Permission", "是否显示": "Show/Hide", "图标": "Icon", "节点路由": "Node Route", "路由缓存": "Route Cache", "文件路径": "File Path", "排序号": "Sorting Number", "节点类型": "Node Type", "节点名称": "Node Name", "上级节点": "<PERSON><PERSON>", "请输入节点路由，如：/test": "Please enter the node route, e.g.: /test", "开启": "Enable", "关闭": "Disable", "请填写排序号": "Please fill in the sorting number", "导入": "Import", "如遇到问题无法导入菜单，请检查文件并尝试重新导入。": "If you encounter problems importing the menu, please check the file and try to import again.", "角色标签": "Character Tag", "请填写新密码": "Please fill in the new password", "保存修改": "Save Changes", "修改成功": "Modification Successful", "拼命加载中": "Loading拼命", "转移": "Transfer", "搜索用户名、姓名": "Search Username, Name", "用户列表": "User List", "用户名": "Username", "姓名": "Name", "部门名称": "Department Name", "角色": "Role", "状态": "Status", "手机号码": "Mobile Phone Number", "选择头像": "Select Avatar", "密码": "Password", "密码长度在 6 到 16 个字符": "Password length should be between 6 and 16 characters", "邮箱": "Email", "启用": "Enable", "禁用": "Disable", "部门转移": "Department Transfer", "请输入备注": "Please enter remarks", "清空": "Clear", "日志保存天数": "Log save days", "搜索请求地址、用户昵称、ip": "Search request address, user nickname, IP", "用户ID": "User ID", "用户昵称": "User nickname", "请求地址": "Request address", "参数": "Parameter", "请求时间": "Request time", "保存成功": "Save successful", "是否要清空日志？": "Do you want to clear the log?", "提示": "Tip", "清空成功": "Clear successful", "基本信息": "Basic information", "头像": "Avatar", "昵称": "Nickname", "请填写昵称": "Please fill in the nickname", "原密码": "Original password", "请填写原密码": "Please fill in the original password", "新密码": "New password", "菜单导入": "Menu Import", "添加": "Add", "导入成功": "Import Success", "{file}文件格式错误：{error}": "{file} File Format Error: {error}", "导出": "Export", "选择菜单": "Select Menu", "请先选择要导出的菜单": "Please select the menu to export first", "菜单数据": "<PERSON>u <PERSON>", "退出登录": "Log out", "确定退出登录吗？": "Are you sure you want to log out?", "搜索关键字": "Search Keyword", "关闭当前": "Close Current", "关闭其他": "Close Others", "关闭所有": "Close All", "{label} 没有子菜单，请先添加": "{label} has no sub-menus. Please add them first", "快速开发后台权限管理系统": "Quick Development Background Permission Management System", "请输入用户名": "Please enter your username", "请输入密码": "Please enter your password", "验证码": "Verification Code", "登录": "Log in", "用户名不能为空": "Username cannot be empty", "密码不能为空": "Password cannot be empty", "图片验证码不能为空": "Image verification code cannot be empty", "验证码获取失败": "Failed to obtain verification code", "马上回来": "Be right back", "糟糕，出了点问题": "Oops, something went wrong", "找不到您要查找的页面": "Page not found", "您无权访问此页面": "You are not authorized to access this page", "认证失败，请重新登录！": "Authentication failed, please log in again!", "返回首页": "Return to home page", "重新登录": "Log in again", "返回登录页": "Return to login page", "自定义输入": "Custom input", "请输入": "Please enter", "输入关键字进行过滤": "Enter keywords for filtering", "复制": "Copy", "行为": "Behavior", "ip": "IP", "数据类型 0-字符串 1-富文本 2-文件 ": "Data type 0 - String 1 - Rich text 2 - File", "键": "Key", "选择部门": "Select Department", "请选择部门": "Please Select Department", "转移到新部门，是否继续？": "Transfer to a new department. Continue?", "转移成功": "Transfer Successful", "组织架构": "Organization Structure", "刷新": "Refresh", "拖动排序": "Drag to Sort", "编辑部门": "Edit Department", "上级部门": "Superior Department", "排序": "Sort", "新增部门 “{name}” 成功": "Successfully added new department “{name}”", "删除成功": "Delete Successful", "“{name}” 部门的用户已成功转移到 “{parentName}” 部门。": "Users in the “{name}” department have been successfully transferred to the “{parentName}” department.", "此操作将会删除 “{name}” 部门的所有用户，是否确认？": "This operation will delete all users in the “{name}” department. Are you sure?", "直接删除": "Delete Directly", "保留用户": "Keep Users", "部门架构已发生改变，是否保存？": "The department structure has changed. Do you want to save?", "更新排序成功": "Successfully updated sorting", "新增": "Add", "编辑": "Edit", "个人中心": "Personal Center", "正在加载资源...": "Loading resources...", "初次加载资源可能需要较多时间，请耐心等待": "It may take some time for the initial resource loading. Please wait patiently.", "搜索名称": "Search Name", "是否关联上下级": "Whether to associate with superiors and subordinates", "名称": "Name", "标识": "Identifier", "备注": "Remarks", "功能权限": "Function Permissions", "数据权限": "Data Permissions", "创建时间": "Creation Time", "更新时间": "Update Time", "数据类型": "Data Type", "搜索名称、keyName": "Search Name, keyName", "字符串": "String", "富文本": "Rich Text", "文件": "File", "请输入Key": "Please enter Key", "类型": "Type", "数据": "Data"}