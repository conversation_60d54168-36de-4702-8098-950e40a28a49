<template>
	<div class="columns-editor">
		<div class="editor-header">
			<span>表格列配置</span>
			<el-button size="small" type="primary" @click="addColumn">添加列</el-button>
		</div>

		<div class="columns-list">
			<div
				v-for="(column, index) in columns"
				:key="index"
				class="column-item"
				:class="{ active: activeIndex === index }"
				@click="selectColumn(index)"
			>
				<div class="column-header">
					<span class="column-title">{{ column.label || column.type || `列${index + 1}` }}</span>
					<div class="column-actions">
						<el-button size="small" text @click.stop="moveColumn(index, -1)" :disabled="index === 0">
							↑
						</el-button>
						<el-button size="small" text @click.stop="moveColumn(index, 1)" :disabled="index === columns.length - 1">
							↓
						</el-button>
						<el-button size="small" text type="danger" @click.stop="removeColumn(index)">
							删除
						</el-button>
					</div>
				</div>

				<div v-if="activeIndex === index" class="column-config">
					<!-- 列类型 -->
					<div class="config-item">
						<label>列类型</label>
						<el-select v-model="column.type" size="small" style="width: 100%">
							<el-option label="普通列" value="" />
							<el-option label="多选框" value="selection" />
							<el-option label="序号" value="index" />
							<el-option label="展开" value="expand" />
							<el-option label="操作" value="op" />
						</el-select>
					</div>

					<!-- 普通列配置 -->
					<template v-if="!column.type || column.type === ''">
						<div class="config-item">
							<label>标题</label>
							<el-input v-model="column.label" size="small" placeholder="列标题" />
						</div>
						<div class="config-item">
							<label>字段名</label>
							<el-input v-model="column.prop" size="small" placeholder="数据字段名" />
						</div>
						<div class="config-item">
							<label>宽度</label>
							<el-input-number v-model="column.width" size="small" style="width: 100%" placeholder="固定宽度" />
						</div>
						<div class="config-item">
							<label>最小宽度</label>
							<el-input-number v-model="column.minWidth" size="small" style="width: 100%" placeholder="最小宽度" />
						</div>
						<div class="config-item">
							<label>排序</label>
							<el-select v-model="column.sortable" size="small" style="width: 100%">
								<el-option label="不排序" value="" />
								<el-option label="可排序" value="true" />
								<el-option label="自定义排序" value="custom" />
								<el-option label="默认升序" value="asc" />
								<el-option label="默认降序" value="desc" />
							</el-select>
						</div>
						<div class="config-item">
							<label>对齐方式</label>
							<el-select v-model="column.align" size="small" style="width: 100%">
								<el-option label="左对齐" value="left" />
								<el-option label="居中" value="center" />
								<el-option label="右对齐" value="right" />
							</el-select>
						</div>
					</template>

					<!-- 操作列配置 -->
					<template v-if="column.type === 'op'">
						<div class="config-item">
							<label>操作按钮</label>
							<el-checkbox-group v-model="column.buttons" size="small">
								<el-checkbox label="info">详情</el-checkbox>
								<el-checkbox label="edit">编辑</el-checkbox>
								<el-checkbox label="delete">删除</el-checkbox>
							</el-checkbox-group>
						</div>
						<div class="config-item">
							<label>列宽</label>
							<el-input-number v-model="column.width" size="small" style="width: 100%" :min="100" />
						</div>
					</template>

					<!-- 选择框列配置 -->
					<template v-if="column.type === 'selection'">
						<div class="config-item">
							<label>列宽</label>
							<el-input-number v-model="column.width" size="small" style="width: 100%" :min="50" />
						</div>
					</template>

					<!-- 序号列配置 -->
					<template v-if="column.type === 'index'">
						<div class="config-item">
							<label>标题</label>
							<el-input v-model="column.label" size="small" placeholder="序号" />
						</div>
						<div class="config-item">
							<label>列宽</label>
							<el-input-number v-model="column.width" size="small" style="width: 100%" :min="50" />
						</div>
					</template>

					<!-- 展开列配置 -->
					<template v-if="column.type === 'expand'">
						<div class="config-item">
							<label>标题</label>
							<el-input v-model="column.label" size="small" placeholder="展开" />
						</div>
						<div class="config-item">
							<label>列宽</label>
							<el-input-number v-model="column.width" size="small" style="width: 100%" :min="50" />
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { TableColumn } from '../../../types';

const props = defineProps<{
	modelValue: TableColumn[];
}>();

const emit = defineEmits<{
	'update:modelValue': [value: TableColumn[]];
}>();

const columns = ref<TableColumn[]>([...props.modelValue]);
const activeIndex = ref(0);

// 监听外部变化
watch(() => props.modelValue, (newValue) => {
	columns.value = [...newValue];
}, { deep: true });

// 监听内部变化
watch(columns, (newValue) => {
	emit('update:modelValue', newValue);
}, { deep: true });

// 选择列
const selectColumn = (index: number) => {
	activeIndex.value = index;
};

// 添加列
const addColumn = () => {
	const newColumn: TableColumn = {
		label: '新列',
		prop: 'newField',
		minWidth: 120
	};
	columns.value.push(newColumn);
	activeIndex.value = columns.value.length - 1;
};

// 删除列
const removeColumn = (index: number) => {
	columns.value.splice(index, 1);
	if (activeIndex.value >= columns.value.length) {
		activeIndex.value = Math.max(0, columns.value.length - 1);
	}
};

// 移动列
const moveColumn = (index: number, direction: number) => {
	const newIndex = index + direction;
	if (newIndex >= 0 && newIndex < columns.value.length) {
		const column = columns.value.splice(index, 1)[0];
		columns.value.splice(newIndex, 0, column);
		activeIndex.value = newIndex;
	}
};
</script>

<style scoped lang="scss">
.columns-editor {
	.editor-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;
		padding-bottom: 8px;
		border-bottom: 1px solid #e4e7ed;

		span {
			font-weight: 500;
			color: #303133;
		}
	}

	.columns-list {
		.column-item {
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			margin-bottom: 8px;
			overflow: hidden;

			&.active {
				border-color: #409eff;
			}

			.column-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background: #f8f9fa;
				cursor: pointer;

				.column-title {
					font-size: 14px;
					color: #303133;
				}

				.column-actions {
					display: flex;
					gap: 4px;
				}
			}

			.column-config {
				padding: 12px;
				background: #fff;

				.config-item {
					margin-bottom: 12px;

					&:last-child {
						margin-bottom: 0;
					}

					label {
						display: block;
						font-size: 12px;
						color: #606266;
						margin-bottom: 4px;
					}
				}
			}
		}
	}
}
</style>
