<template>
	<div class="canvas-panel">
		<!-- 工具栏 -->
		<div class="canvas-toolbar">
			<div class="toolbar-left">
				<el-button-group>
					<el-button
						:disabled="!builderStore.canUndo"
						@click="builderStore.undo"
						title="撤销"
					>
						撤销
					</el-button>
					<el-button
						:disabled="!builderStore.canRedo"
						@click="builderStore.redo"
						title="重做"
					>
						重做
					</el-button>
				</el-button-group>

				<el-divider direction="vertical" />

				<el-button
					type="danger"
					:disabled="builderStore.currentPage.components.length === 0"
					@click="handleClearPage"
					title="清空画布"
				>
					清空
				</el-button>
			</div>

			<div class="toolbar-center">
				<span class="page-title">{{ builderStore.currentPage.title }}</span>
			</div>

			<div class="toolbar-right">
				<!-- 设备尺寸切换 -->
				<div class="device-selector">
					<el-select
						v-model="currentDevice"
						@change="handleDeviceChange"
						style="width: 120px"
					>
						<el-option
							v-for="device in devices"
							:key="device.key"
							:label="device.label"
							:value="device.key"
						>
							<span>{{ device.icon }} {{ device.label }}</span>
						</el-option>
					</el-select>
				</div>

				<el-divider direction="vertical" />

				<el-button-group>
					<el-button
						:type="builderStore.editorState.previewMode ? 'default' : 'primary'"
						@click="builderStore.setPreviewMode(false)"
					>
						编辑
					</el-button>
					<el-button
						:type="builderStore.editorState.previewMode ? 'primary' : 'default'"
						@click="builderStore.setPreviewMode(true)"
					>
						预览
					</el-button>
				</el-button-group>
			</div>
		</div>

		<!-- 画布区域 -->
		<div class="canvas-container">
			<div class="canvas-wrapper">
				<div
					class="canvas"
					:class="{
						'preview-mode': builderStore.editorState.previewMode,
						[`device-${currentDevice}`]: true
					}"
					:style="canvasStyle"
					@drop="handleDrop"
					@dragover="handleDragOver"
					@click="handleCanvasClick"
				>
					<!-- 空状态 -->
					<div
						v-if="builderStore.currentPage.components.length === 0"
						class="empty-canvas"
					>
						<div class="empty-icon">📦</div>
						<p>拖拽组件到此处开始设计</p>
					</div>

					<!-- 组件渲染 -->
					<component-renderer
						v-for="component in builderStore.currentPage.components"
						:key="component.id"
						:component="component"
						:is-preview="builderStore.editorState.previewMode"
						@select="handleSelectComponent"
						@delete="handleDeleteComponent"
					/>

					<!-- 拖拽指示器 -->
					<div
						v-if="dragIndicator.show"
						class="drag-indicator"
						:style="dragIndicator.style"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useBuilderStore } from '../../store/builder';
import ComponentRenderer from './component-renderer.vue';
import type { CrudComponentConfig, CrudComponentInstance } from '../../types';

const builderStore = useBuilderStore();

// 设备类型定义
interface Device {
	key: string;
	label: string;
	icon: string;
	width: number;
	height: number;
}

// 预设设备尺寸
const devices: Device[] = [
	{ key: 'desktop', label: 'PC端', icon: '🖥️', width: 1280, height: 800 },
	{ key: 'laptop', label: '笔记本', icon: '💻', width: 1024, height: 768 },
	{ key: 'tablet', label: '平板', icon: '📱', width: 768, height: 1024 },
	{ key: 'mobile', label: '手机', icon: '📱', width: 375, height: 667 }
];

// 当前设备
const currentDevice = ref('desktop');

// 当前设备配置
const deviceConfig = computed(() => {
	return devices.find(d => d.key === currentDevice.value) || devices[0];
});

// 画布样式
const canvasStyle = computed(() => ({
	width: `${deviceConfig.value.width}px`,
	minHeight: `${deviceConfig.value.height}px`,
	paddingTop: '40px'
}));

// 拖拽指示器
const dragIndicator = reactive({
	show: false,
	style: {}
});

// 处理拖拽放置
const handleDrop = (event: DragEvent) => {
	event.preventDefault();
	dragIndicator.show = false;

	try {
		const data = event.dataTransfer?.getData('application/json');
		if (data) {
			const dragData = JSON.parse(data);

			if (dragData.type === 'component') {
				const component = dragData.data as CrudComponentConfig;
				builderStore.addComponent(component);
				ElMessage.success(`已添加 ${component.label} 组件`);
			}
		}
	} catch (error) {
		console.error('拖拽数据解析失败:', error);
	}
};

// 处理拖拽悬停
const handleDragOver = (event: DragEvent) => {
	event.preventDefault();
	event.dataTransfer!.dropEffect = 'copy';

	// 显示拖拽指示器
	const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
	dragIndicator.show = true;
	dragIndicator.style = {
		left: `${event.clientX - rect.left}px`,
		top: `${event.clientY - rect.top}px`
	};
};

// 处理画布点击
const handleCanvasClick = (event: MouseEvent) => {
	if (event.target === event.currentTarget) {
		builderStore.selectComponent(undefined);
	}
};

// 处理组件选择
const handleSelectComponent = (component: CrudComponentInstance) => {
	builderStore.selectComponent(component);
};

// 处理组件删除
const handleDeleteComponent = (component: CrudComponentInstance) => {
	builderStore.removeComponent(component.id);
};

// 处理设备切换
const handleDeviceChange = (deviceKey: string) => {
	currentDevice.value = deviceKey;
	const device = devices.find(d => d.key === deviceKey);
	if (device) {
		ElMessage.success(`已切换到${device.label}视图 (${device.width}x${device.height})`);
	}
};

// 处理清空画布
const handleClearPage = () => {
	ElMessageBox.confirm('确定要清空画布吗？此操作不可恢复。', '确认清空', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			builderStore.clearPage();
			ElMessage.success('画布已清空');
		})
		.catch(() => {
			// 用户取消操作
		});
};
</script>

<style scoped lang="scss">
.canvas-panel {
	flex: 1;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;

	.canvas-toolbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;
		background: #fff;
		border-bottom: 1px solid #e4e7ed;
		box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

		.toolbar-left,
		.toolbar-right {
			display: flex;
			align-items: center;
			gap: 12px;
		}

		.device-selector {
			.el-select {
				.el-input__wrapper {
					border-radius: 6px;
				}
			}
		}

		.toolbar-center {
			.page-title {
				font-size: 16px;
				font-weight: 600;
				color: #303133;
			}
		}

		.scale-control {
			display: flex;
			align-items: center;
			gap: 8px;

			.scale-text {
				min-width: 40px;
				text-align: center;
				font-size: 12px;
				color: #606266;
			}
		}
	}

	.canvas-container {
		flex: 1;
		overflow: auto;
		padding: 20px;

		.canvas-wrapper {
			display: flex;
			justify-content: center;
			min-height: 100%;

			.canvas {
				background: #fff;
				border: 1px solid #e4e7ed;
				border-radius: 8px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				position: relative;
				overflow: hidden;
				transition: all 0.3s ease;

				&.preview-mode {
					border-color: #67c23a;
					box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
				}

				// 设备特定样式
				&.device-desktop {
					border-radius: 8px;
				}

				&.device-laptop {
					border-radius: 8px;
				}

				&.device-tablet {
					border-radius: 12px;
					box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
				}

				&.device-mobile {
					border-radius: 20px;
					box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
					border: 2px solid #333;
				}

				.empty-canvas {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					height: 400px;
					color: #909399;

					.empty-icon {
						font-size: 64px;
						margin-bottom: 16px;
						opacity: 0.5;
					}

					p {
						font-size: 14px;
						margin: 0;
					}
				}

				.drag-indicator {
					position: absolute;
					width: 2px;
					height: 20px;
					background: #409eff;
					pointer-events: none;
					z-index: 1000;
				}
			}
		}
	}
}

// 滚动条样式
.canvas-container::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

.canvas-container::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 4px;
}

.canvas-container::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 4px;

	&:hover {
		background: #a8a8a8;
	}
}
</style>
