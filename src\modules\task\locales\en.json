{"编辑": "Edit", "删除": "Delete", "查看日志": "View Log", "状态": "Status", "成功": "Success", "失败": "Failure", "描述": "Description", "执行状态": "Execution Status", "执行时间": "Execution Time", "日志列表（{name}）": "Log List ({name})", "状态 0-停止 1-运行": "Status 0 - Stopped 1 - Running", "状态 0-系统 1-用户": "Status 0 - System 1 - User", "执行服务": "Execute Service", "定时规则": "Scheduled Rule", "间隔{every}秒执行": "Execute every {every} seconds", "进行中": "In Progress", "已停止": "Stopped", "添加计划任务": "Add Scheduled Task", "此操作将启用任务（{name}），是否继续？": "This operation will enable the task ({name}), do you want to continue?", "此操作将停用任务（{name}），是否继续？": "This operation will disable the task ({name}), do you want to continue?", "此操作将删除任务（{name}），是否继续？": "This operation will delete the task ({name}), do you want to continue?", "编辑计划任务": "Edit Scheduled Task", "名称": "Name", "类型": "Type", "时间间隔": "Time Interval", "间隔(秒)": "Interval (seconds)", "开始时间": "Start Time", "备注": "Remarks", "保存成功": "Save Success", "暂停": "Pause", "开始": "Start", "立即执行": "Execute Immediately"}