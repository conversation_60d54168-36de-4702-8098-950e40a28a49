<template>
	<div class="test-code-generation">
		<h2>代码生成测试页面</h2>
		<p>测试CRUD组件和普通组件的代码生成</p>
		
		<div class="test-sections">
			<!-- CRUD页面测试 -->
			<el-card class="test-card">
				<template #header>
					<h3>CRUD页面代码生成测试</h3>
				</template>
				<div class="test-content">
					<el-button @click="testCrudGeneration" type="primary">生成CRUD代码</el-button>
					<el-button @click="showCrudCode = !showCrudCode">
						{{ showCrudCode ? '隐藏' : '显示' }}代码
					</el-button>
				</div>
				<div v-if="showCrudCode" class="code-display">
					<pre><code>{{ crudCode }}</code></pre>
				</div>
			</el-card>

			<!-- 普通页面测试 -->
			<el-card class="test-card">
				<template #header>
					<h3>普通页面代码生成测试</h3>
				</template>
				<div class="test-content">
					<el-button @click="testStandardGeneration" type="success">生成标准代码</el-button>
					<el-button @click="showStandardCode = !showStandardCode">
						{{ showStandardCode ? '隐藏' : '显示' }}代码
					</el-button>
				</div>
				<div v-if="showStandardCode" class="code-display">
					<pre><code>{{ standardCode }}</code></pre>
				</div>
			</el-card>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CrudCodeGenerator } from '../utils/crud-code-generator';
import type { CrudPageConfig } from '../types';

defineOptions({
	name: 'test-code-generation'
});

const showCrudCode = ref(false);
const showStandardCode = ref(false);
const crudCode = ref('');
const standardCode = ref('');

// 测试CRUD代码生成
const testCrudGeneration = () => {
	const crudPage: CrudPageConfig = {
		name: 'test-crud',
		title: '测试CRUD页面',
		components: [
			{
				id: 'crud-1',
				componentId: 'cl-crud',
				label: 'CRUD容器',
				props: { service: 'test' },
				style: {},
				children: [
					{
						id: 'table-1',
						componentId: 'cl-table',
						label: '表格',
						props: {},
						style: {},
						parentId: 'crud-1'
					},
					{
						id: 'upsert-1',
						componentId: 'cl-upsert',
						label: '新增编辑',
						props: {},
						style: {},
						parentId: 'crud-1'
					}
				]
			}
		]
	};

	const generator = new CrudCodeGenerator(crudPage);
	crudCode.value = generator.generateVueCode();
	showCrudCode.value = true;
};

// 测试标准代码生成
const testStandardGeneration = () => {
	const standardPage: CrudPageConfig = {
		name: 'test-standard',
		title: '测试标准页面',
		components: [
			{
				id: 'input-1',
				componentId: 'form-item-input',
				label: '输入框',
				props: { 
					label: '用户名', 
					prop: 'username',
					placeholder: '请输入用户名'
				},
				style: {}
			},
			{
				id: 'select-1',
				componentId: 'form-item-select',
				label: '选择器',
				props: { 
					label: '状态', 
					prop: 'status',
					options: [
						{ label: '启用', value: '1' },
						{ label: '禁用', value: '0' }
					]
				},
				style: {}
			},
			{
				id: 'btn-1',
				componentId: 'cl-add-btn',
				label: '新增按钮',
				props: {},
				style: {}
			}
		]
	};

	const generator = new CrudCodeGenerator(standardPage);
	standardCode.value = generator.generateVueCode();
	showStandardCode.value = true;
};
</script>

<style scoped lang="scss">
.test-code-generation {
	padding: 20px;
	max-width: 1200px;
	margin: 0 auto;

	h2 {
		color: #303133;
		margin-bottom: 10px;
	}

	.test-sections {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
		margin-top: 20px;

		@media (max-width: 768px) {
			grid-template-columns: 1fr;
		}
	}

	.test-card {
		.test-content {
			margin-bottom: 20px;

			.el-button {
				margin-right: 10px;
			}
		}

		.code-display {
			background: #f5f5f5;
			border-radius: 4px;
			padding: 16px;
			max-height: 400px;
			overflow-y: auto;

			pre {
				margin: 0;
				white-space: pre-wrap;
				word-wrap: break-word;

				code {
					font-family: 'Courier New', monospace;
					font-size: 12px;
					line-height: 1.4;
					color: #333;
				}
			}
		}
	}
}
</style>
