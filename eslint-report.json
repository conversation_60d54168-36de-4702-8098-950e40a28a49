[{"filePath": "D:\\code\\ai\\ai-admin\\env.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\eslint.config.js", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\postcss.config.js", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\App.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\config\\dev.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\config\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\config\\prod.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\config\\proxy.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\bootstrap\\eps.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\bootstrap\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\bootstrap\\module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\hooks\\browser.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\hooks\\hmr.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\hooks\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\hooks\\mitt.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\module\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\router\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\service\\base.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\service\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\service\\request.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\service\\stream.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\types\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\utils\\loading.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\cool\\utils\\storage.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\main.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\avatar\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\code\\json.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\dept\\check.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\dept\\select.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\editor\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\icon\\svg.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\image\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\link\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\menu\\check.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\menu\\file.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\menu\\icon.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\menu\\perms.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\menu\\select.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\components\\num\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\directives\\permission.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\error\\401.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\error\\403.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\error\\404.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\error\\500.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\error\\502.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\error\\components\\error-page.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\login\\components\\pic-captcha.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\login\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\amenu.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\bmenu.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\global.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\process.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\route-nav.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\slider.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\topbar.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\components\\views.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\pages\\main\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\store\\app.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\store\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\store\\menu.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\store\\process.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\store\\user.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\types\\index.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\utils\\permission.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\frame.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\info.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\log.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\menu\\components\\exp.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\menu\\components\\imp.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\menu\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\param.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\role.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\user\\components\\dept-list.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\user\\components\\user-move.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\base\\views\\user\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\directives\\color.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\adv-search\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\adv-search\\custom.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\code.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\all.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\dict.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\event.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\select-table.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\service.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\crud\\user-select.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\children.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\component\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\component\\select-labels.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\component\\select-status.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\component\\select-work.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\component\\select-work2.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\config.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\crud.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\disabled.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\event.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\group.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\hidden.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\layout.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\open.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\options.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\plugin\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\plugin\\role.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\required.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\rules.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\form\\setFocus.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\other\\context-menu.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\other\\tips.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\other\\tsx\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\search\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\search\\collapse.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\search\\custom.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\search\\layout.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\search\\plugin.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\children.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\column-custom.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\component\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\component\\user-info.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\context-menu.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\dict.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\formatter.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\hidden.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\op.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\plugin\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\plugin\\row-edit.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\plugin\\to-tree.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\search.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\selection.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\slot.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\span-method.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\table\\summary.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\upsert\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\upsert\\event.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\upsert\\hook\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\upsert\\hook\\reg-pca2.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\components\\upsert\\mode.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\crud\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\category-ratio.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\count-effect.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\count-paid.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\count-user.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\count-views.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\hot-goods.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\components\\tab-chart.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\home\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\demo\\views\\test\\route.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\store\\dict.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\store\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\types\\index.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\dict\\views\\list.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\components\\ai-code\\btn.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\components\\ai-code\\dev.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\components\\auto-menu.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\components\\auto-perms.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\hooks\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\hooks\\menu.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\hooks\\plugin.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\types\\index.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\views\\ai-code.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\helper\\views\\plugins.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\recycle\\views\\data.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\space\\components\\space-inner.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\space\\components\\space.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\space\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\space\\hooks\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\space\\views\\list.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\task\\components\\logs.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\task\\views\\list.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\user\\components\\user-select.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\user\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\modules\\user\\views\\list.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\comm\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\column-custom\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\date\\picker.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\date\\text.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\dict\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\number\\range.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\render\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\select\\button.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\select\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\select\\table.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\switch\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\text\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\components\\user\\select.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\form\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\form\\setFocus.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\search\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\search\\setAuto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\table\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\table\\rowEdit.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\crud\\plugins\\table\\toTree.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\components\\account.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\components\\dict.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\components\\doc.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\components\\eps.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\components\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\components\\proxy.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\dev-tools\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\distpicker\\components\\index.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\distpicker\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\distpicker\\demo\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\echarts\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\editor-preview\\components\\preview.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\editor-preview\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\editor-preview\\demo\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\editor-wang\\components\\wang.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\editor-wang\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\editor-wang\\demo\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\element-ui\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\excel\\components\\export-btn.tsx", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\excel\\components\\import-btn.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\excel\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\excel\\demo\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\excel\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\github\\components\\code.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\github\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\i18n\\components\\switch.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\i18n\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\i18n\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\iconfont\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\iconfont\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\iconfont\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\tailwind\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\theme\\components\\theme.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\theme\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\theme\\hooks\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\theme\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\theme\\types\\index.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\theme\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\components\\upload-item\\index.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\components\\upload-item\\viewer.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\components\\upload.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\base.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\check.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\custom.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\drag.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\file.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\multiple.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\small.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\demo\\space.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\hooks\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\types\\index.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\upload\\utils\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\components\\group.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\components\\head.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\demo\\group.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\demo\\head.vue", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\hooks\\group.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\hooks\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\plugins\\view\\types\\index.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\src\\shims-vue.d.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\tailwind.config.js", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}, {"filePath": "D:\\code\\ai\\ai-admin\\vite.config.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "vue/component-tags-order", "replacedBy": ["block-order"]}]}]