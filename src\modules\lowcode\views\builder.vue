<template>
	<div class="lowcode-builder">
		<!-- 顶部工具栏 -->
		<div class="builder-header">
			<div class="header-left">
				<h2>低代码页面构建器</h2>
			</div>

			<div class="header-center">
				<el-button-group>
					<el-button :icon="FolderOpened" @click="handleOpenPage"> 打开 </el-button>
					<el-button :icon="DocumentAdd" @click="handleNewPage"> 新建 </el-button>
					<el-button :icon="Document" @click="handleSavePage"> 保存 </el-button>
				</el-button-group>

				<el-divider direction="vertical" />

				<el-button
					type="danger"
					:disabled="builderStore.currentPage.components.length === 0"
					@click="handleClearPage"
					title="清空画布"
				>
					清空
				</el-button>
			</div>

			<div class="header-right">
				<el-button-group>
					<el-button :icon="Download" @click="handleExportCode"> 导出代码 </el-button>
					<el-button :icon="View" type="primary" @click="handlePreview"> 预览 </el-button>
				</el-button-group>
			</div>
		</div>

		<!-- 主体区域 -->
		<div class="builder-body">
			<!-- 左侧组件面板 -->
			<component-panel />

			<!-- 中间画布区域 -->
			<canvas-panel />

			<!-- 右侧属性面板 -->
			<property-panel />
		</div>

		<!-- 页面管理对话框 -->
		<el-dialog v-model="pageDialog.visible" :title="pageDialog.title" width="800px">
			<div v-if="pageDialog.type === 'list'">
				<div class="page-list">
					<div class="list-header">
						<el-button type="primary" @click="handleNewPage">新建页面</el-button>
					</div>
					<div class="list-content">
						<p>页面列表功能开发中...</p>
					</div>
				</div>
			</div>

			<div v-else-if="pageDialog.type === 'form'">
				<el-form
					ref="pageFormRef"
					:model="pageForm"
					:rules="pageFormRules"
					label-width="100px"
				>
					<el-form-item label="页面名称" prop="name">
						<el-input v-model="pageForm.name" placeholder="请输入页面名称" />
					</el-form-item>
					<el-form-item label="页面标题" prop="title">
						<el-input v-model="pageForm.title" placeholder="请输入页面标题" />
					</el-form-item>
					<el-form-item label="页面描述" prop="description">
						<el-input
							v-model="pageForm.description"
							type="textarea"
							:rows="3"
							placeholder="请输入页面描述"
						/>
					</el-form-item>
				</el-form>
			</div>

			<template #footer>
				<el-button @click="pageDialog.visible = false">取消</el-button>
				<el-button
					v-if="pageDialog.type === 'form'"
					type="primary"
					@click="handleSavePageForm"
				>
					保存
				</el-button>
			</template>
		</el-dialog>

		<!-- 代码预览对话框 -->
		<el-dialog
			v-model="codeDialog.visible"
			title="导出代码"
			width="80%"
			:fullscreen="codeDialog.fullscreen"
		>
			<div class="code-preview">
				<div class="code-toolbar">
					<el-button-group>
						<el-button
							:type="codeDialog.activeTab === 'vue' ? 'primary' : 'default'"
							@click="codeDialog.activeTab = 'vue'"
						>
							Vue 组件
						</el-button>
						<el-button
							:type="codeDialog.activeTab === 'json' ? 'primary' : 'default'"
							@click="codeDialog.activeTab = 'json'"
						>
							JSON 配置
						</el-button>
					</el-button-group>

					<div class="toolbar-right">
						<el-button
							:icon="FullScreen"
							@click="codeDialog.fullscreen = !codeDialog.fullscreen"
						/>
						<el-button :icon="DocumentCopy" @click="handleCopyCode"> 复制 </el-button>
					</div>
				</div>

				<div class="code-content">
					<pre v-if="codeDialog.activeTab === 'vue'" class="code-block">{{
						generatedVueCode
					}}</pre>
					<pre v-else class="code-block">{{ generatedJsonCode }}</pre>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
	FolderOpened,
	DocumentAdd,
	Document,
	Download,
	View,
	DocumentCopy,
	FullScreen
} from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import { useBuilderStore } from '../store/builder';
import ComponentPanel from '../components/component-panel/index.vue';
import CanvasPanel from '../components/canvas-panel/index.vue';
import PropertyPanel from '../components/property-panel/index.vue';
import type { CrudPageConfig } from '../types';
import { CrudCodeGenerator } from '../utils/crud-code-generator';

defineOptions({
	name: 'lowcode-builder'
});

const { service } = useCool();
const builderStore = useBuilderStore();

// 页面对话框状态
const pageDialog = reactive({
	visible: false,
	type: 'list' as 'list' | 'form',
	title: '页面管理'
});

// 页面表单
const pageFormRef = ref();
const pageForm = reactive<CrudPageConfig>({
	name: '',
	title: '',
	description: '',
	components: []
});

const pageFormRules = {
	name: [{ required: true, message: '请输入页面名称', trigger: 'blur' }],
	title: [{ required: true, message: '请输入页面标题', trigger: 'blur' }]
};

// 代码预览对话框
const codeDialog = reactive({
	visible: false,
	fullscreen: false,
	activeTab: 'vue' as 'vue' | 'json'
});

// 生成的代码
const generatedVueCode = computed(() => {
	return generateVueCode(builderStore.currentPage);
});

const generatedJsonCode = computed(() => {
	return JSON.stringify(builderStore.exportPageConfig(), null, 2);
});

// 页面管理相关配置（简化版）

// 方法
const handleNewPage = () => {
	pageDialog.type = 'form';
	pageDialog.title = '新建页面';
	pageDialog.visible = true;

	// 重置表单
	Object.assign(pageForm, {
		name: '',
		title: '',
		description: '',
		components: []
	});
};

const handleOpenPage = () => {
	pageDialog.type = 'list';
	pageDialog.title = '选择页面';
	pageDialog.visible = true;
};

const handleSavePage = async () => {
	try {
		const pageConfig = builderStore.exportPageConfig();
		console.log('保存页面配置:', pageConfig);
		ElMessage.success('页面已保存');
	} catch (error) {
		ElMessage.error('保存失败');
	}
};

const handleSavePageForm = async () => {
	try {
		await pageFormRef.value?.validate();

		// 创建新页面
		const newPage: CrudPageConfig = {
			...pageForm,
			components: []
		};

		builderStore.loadPage(newPage);
		pageDialog.visible = false;
		ElMessage.success('页面已创建');
	} catch (error) {
		// 验证失败
	}
};

const handleExportCode = () => {
	codeDialog.visible = true;
	codeDialog.activeTab = 'vue';
};

const handlePreview = () => {
	builderStore.setPreviewMode(true);
	ElMessage.success('已切换到预览模式');
};

const handleCopyCode = async () => {
	try {
		const code =
			codeDialog.activeTab === 'vue' ? generatedVueCode.value : generatedJsonCode.value;
		await navigator.clipboard.writeText(code);
		ElMessage.success('代码已复制到剪贴板');
	} catch (error) {
		ElMessage.error('复制失败');
	}
};

const handleClearPage = () => {
	ElMessageBox.confirm('确定要清空画布吗？此操作不可恢复。', '确认清空', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			builderStore.clearPage();
			ElMessage.success('画布已清空');
		})
		.catch(() => {
			// 用户取消操作
		});
};

// 生成 Vue 代码
const generateVueCode = (page: CrudPageConfig): string => {
	const generator = new CrudCodeGenerator(page);
	return generator.generateVueCode();
};

// 生命周期
onMounted(() => {
	builderStore.init();
});
</script>

<style scoped lang="scss">
.lowcode-builder {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;

	.builder-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 20px;
		background: #fff;
		border-bottom: 1px solid #e4e7ed;
		box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

		.header-left h2 {
			margin: 0;
			font-size: 18px;
			font-weight: 600;
			color: #303133;
		}

		.header-center,
		.header-right {
			display: flex;
			align-items: center;
			gap: 12px;
		}
	}

	.builder-body {
		flex: 1;
		display: flex;
		overflow: hidden;
	}
}

.code-preview {
	.code-toolbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16px;
		padding-bottom: 12px;
		border-bottom: 1px solid #e4e7ed;

		.toolbar-right {
			display: flex;
			gap: 8px;
		}
	}

	.code-content {
		.code-block {
			background: #f8f9fa;
			border: 1px solid #e4e7ed;
			border-radius: 6px;
			padding: 16px;
			font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
			font-size: 12px;
			line-height: 1.5;
			overflow-x: auto;
			white-space: pre-wrap;
			word-wrap: break-word;
			max-height: 500px;
			overflow-y: auto;
		}
	}
}
</style>
