import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { cloneDeep, uniqueId } from 'lodash-es';
import type {
	CrudComponentConfig,
	CrudComponentInstance,
	CrudPageConfig,
	CrudEditorState
} from '../types';
import { CRUD_BUILTIN_COMPONENTS } from '../types';

export const useBuilderStore = defineStore('lowcode-builder', () => {
	// 状态
	const currentPage = ref<CrudPageConfig>({
		name: 'untitled',
		title: '未命名CRUD页面',
		components: [],
		globalStyle: {}
	});

	const editorState = ref<CrudEditorState>({
		history: [],
		historyIndex: -1,
		previewMode: false,
		scale: 1
	});

	const availableComponents = ref<CrudComponentConfig[]>(CRUD_BUILTIN_COMPONENTS);

	// 计算属性
	const selectedComponent = computed(() => editorState.value.selectedComponent);
	const canUndo = computed(() => editorState.value.historyIndex > 0);
	const canRedo = computed(
		() => editorState.value.historyIndex < editorState.value.history.length - 1
	);

	// 方法
	function generateId(): string {
		return uniqueId('component_');
	}

	function saveToHistory() {
		const history = editorState.value.history;
		const currentIndex = editorState.value.historyIndex;

		// 删除当前位置之后的历史记录
		history.splice(currentIndex + 1);

		// 添加新的历史记录
		history.push(cloneDeep(currentPage.value));
		editorState.value.historyIndex = history.length - 1;

		// 限制历史记录数量
		if (history.length > 50) {
			history.shift();
			editorState.value.historyIndex--;
		}
	}

	function undo() {
		if (canUndo.value) {
			editorState.value.historyIndex--;
			currentPage.value = cloneDeep(
				editorState.value.history[editorState.value.historyIndex]
			);
		}
	}

	function redo() {
		if (canRedo.value) {
			editorState.value.historyIndex++;
			currentPage.value = cloneDeep(
				editorState.value.history[editorState.value.historyIndex]
			);
		}
	}

	function selectComponent(component: CrudComponentInstance | undefined) {
		editorState.value.selectedComponent = component;
	}

	function hoverComponent(component: CrudComponentInstance | undefined) {
		editorState.value.hoveredComponent = component;
	}

	function addComponent(
		componentConfig: CrudComponentConfig,
		parentId?: string,
		index?: number
	): CrudComponentInstance {
		const instance: CrudComponentInstance = {
			id: generateId(),
			componentId: componentConfig.id,
			label: componentConfig.label,
			props: cloneDeep(componentConfig.defaultProps),
			style: {},
			children: componentConfig.children ? [] : undefined,
			parentId
		};

		if (parentId) {
			const parent = findComponentById(parentId);
			if (parent && parent.children) {
				if (index !== undefined) {
					parent.children.splice(index, 0, instance);
				} else {
					parent.children.push(instance);
				}
			}
		} else {
			if (index !== undefined) {
				currentPage.value.components.splice(index, 0, instance);
			} else {
				currentPage.value.components.push(instance);
			}
		}

		saveToHistory();
		selectComponent(instance);
		return instance;
	}

	function removeComponent(componentId: string) {
		function removeFromArray(components: CrudComponentInstance[]): boolean {
			const index = components.findIndex(c => c.id === componentId);
			if (index !== -1) {
				components.splice(index, 1);
				return true;
			}

			for (const component of components) {
				if (component.children && removeFromArray(component.children)) {
					return true;
				}
			}
			return false;
		}

		if (removeFromArray(currentPage.value.components)) {
			saveToHistory();
			selectComponent(undefined);
			ElMessage.success('组件已删除');
		}
	}

	function updateComponentProps(componentId: string, props: Record<string, any>) {
		const component = findComponentById(componentId);
		if (component) {
			Object.assign(component.props, props);
			saveToHistory();
		}
	}

	function updateComponentStyle(componentId: string, style: Record<string, any>) {
		const component = findComponentById(componentId);
		if (component) {
			Object.assign(component.style, style);
			saveToHistory();
		}
	}

	function findComponentById(id: string): CrudComponentInstance | undefined {
		function search(components: CrudComponentInstance[]): CrudComponentInstance | undefined {
			for (const component of components) {
				if (component.id === id) {
					return component;
				}
				if (component.children) {
					const found = search(component.children);
					if (found) return found;
				}
			}
			return undefined;
		}
		return search(currentPage.value.components);
	}

	function moveComponent(componentId: string, targetParentId: string | undefined, index: number) {
		const component = findComponentById(componentId);
		if (!component) return;

		// 先从原位置移除
		removeComponent(componentId);

		// 添加到新位置
		if (targetParentId) {
			const parent = findComponentById(targetParentId);
			if (parent && parent.children) {
				parent.children.splice(index, 0, component);
				component.parentId = targetParentId;
			}
		} else {
			currentPage.value.components.splice(index, 0, component);
			component.parentId = undefined;
		}

		saveToHistory();
	}

	function copyComponent(componentId: string): CrudComponentInstance | undefined {
		const component = findComponentById(componentId);
		if (component) {
			const copied = cloneDeep(component);
			// 重新生成ID
			function regenerateIds(comp: CrudComponentInstance) {
				comp.id = generateId();
				if (comp.children) {
					comp.children.forEach(regenerateIds);
				}
			}
			regenerateIds(copied);
			return copied;
		}
		return undefined;
	}

	function pasteComponent(component: CrudComponentInstance, parentId?: string, index?: number) {
		if (parentId) {
			const parent = findComponentById(parentId);
			if (parent && parent.children) {
				if (index !== undefined) {
					parent.children.splice(index, 0, component);
				} else {
					parent.children.push(component);
				}
				component.parentId = parentId;
			}
		} else {
			if (index !== undefined) {
				currentPage.value.components.splice(index, 0, component);
			} else {
				currentPage.value.components.push(component);
			}
			component.parentId = undefined;
		}

		saveToHistory();
		selectComponent(component);
	}

	function moveComponentUp(componentId: string): boolean {
		const component = findComponentById(componentId);
		if (!component) return false;

		let components: CrudComponentInstance[];
		if (component.parentId) {
			const parent = findComponentById(component.parentId);
			if (!parent || !parent.children) return false;
			components = parent.children;
		} else {
			components = currentPage.value.components;
		}

		const index = components.findIndex(c => c.id === componentId);
		if (index <= 0) return false; // 已经是第一个或未找到

		// 交换位置
		[components[index], components[index - 1]] = [components[index - 1], components[index]];

		saveToHistory();
		ElMessage.success('组件已上移');
		return true;
	}

	function moveComponentDown(componentId: string): boolean {
		const component = findComponentById(componentId);
		if (!component) return false;

		let components: CrudComponentInstance[];
		if (component.parentId) {
			const parent = findComponentById(component.parentId);
			if (!parent || !parent.children) return false;
			components = parent.children;
		} else {
			components = currentPage.value.components;
		}

		const index = components.findIndex(c => c.id === componentId);
		if (index === -1 || index >= components.length - 1) return false; // 已经是最后一个或未找到

		// 交换位置
		[components[index], components[index + 1]] = [components[index + 1], components[index]];

		saveToHistory();
		ElMessage.success('组件已下移');
		return true;
	}

	function clearPage() {
		currentPage.value.components = [];
		saveToHistory();
		selectComponent(undefined);
	}

	function setPreviewMode(preview: boolean) {
		editorState.value.previewMode = preview;
		if (preview) {
			selectComponent(undefined);
		}
	}

	function setScale(scale: number) {
		editorState.value.scale = Math.max(0.1, Math.min(2, scale));
	}

	function loadPage(page: CrudPageConfig) {
		currentPage.value = cloneDeep(page);
		editorState.value.history = [cloneDeep(page)];
		editorState.value.historyIndex = 0;
		selectComponent(undefined);
	}

	function exportPageConfig(): CrudPageConfig {
		return cloneDeep(currentPage.value);
	}

	// 初始化
	function init() {
		editorState.value.history = [cloneDeep(currentPage.value)];
		editorState.value.historyIndex = 0;
	}

	return {
		// 状态
		currentPage,
		editorState,
		availableComponents,

		// 计算属性
		selectedComponent,
		canUndo,
		canRedo,

		// 方法
		init,
		selectComponent,
		hoverComponent,
		addComponent,
		removeComponent,
		updateComponentProps,
		updateComponentStyle,
		findComponentById,
		moveComponent,
		moveComponentUp,
		moveComponentDown,
		copyComponent,
		pasteComponent,
		clearPage,
		setPreviewMode,
		setScale,
		loadPage,
		exportPageConfig,
		undo,
		redo,
		saveToHistory
	};
});
