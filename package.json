{"name": "cool-admin-vue", "version": "8.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "build-static": "vite build --mode static", "build-demo": "vite build --mode demo", "preview": "vite preview", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@cool-vue/crud": "^8.0.4", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.9", "chardet": "^2.0.0", "core-js": "^3.40.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.3", "file-saver": "^2.0.5", "lodash-es": "^4.17.21", "marked": "^14.1.3", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.3.1", "store": "^2.0.12", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.0.1", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@cool-vue/vite-plugin": "^8.1.2", "@intlify/unplugin-vue-i18n": "^6.0.3", "@rushstack/eslint-patch": "^1.10.5", "@tsconfig/node20": "^20.1.4", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.17.17", "@types/nprogress": "^0.2.3", "@types/store": "^2.0.5", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.81.0", "tailwindcss": "^3.4.17", "terser": "^5.36.0", "typescript": "~5.5.4", "vite": "^5.4.14", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.1", "vue-tsc": "^2.2.0"}}