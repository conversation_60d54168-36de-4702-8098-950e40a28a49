<template>
	<div class="component-panel">
		<div class="panel-header">
			<h3>CRUD组件库</h3>
			<p class="panel-desc">拖拽组件到画布中构建CRUD页面</p>
		</div>

		<div class="panel-content">
			<!-- Tab 切换 -->
			<el-tabs v-model="activeTab" class="panel-tabs">
				<!-- 组件 Tab -->
				<el-tab-pane label="组件" name="components">
					<div class="component-categories">
						<div
							v-for="category in categories"
							:key="category.key"
							class="category-section"
						>
							<div class="category-title" @click="toggleCategory(category.key)">
								<span class="category-icon">{{ category.icon }}</span>
								<span class="category-label">{{ category.label }}</span>
								<span class="category-toggle">
									<el-icon>
										<arrow-down v-if="!collapsedCategories[category.key]" />
										<arrow-right v-else />
									</el-icon>
								</span>
							</div>

							<el-collapse-transition>
								<div
									v-show="!collapsedCategories[category.key]"
									class="component-list"
								>
									<div
										v-for="component in getComponentsByCategory(category.key)"
										:key="component.id"
										class="component-item"
										:draggable="true"
										@dragstart="handleDragStart(component, $event)"
										@dragend="handleDragEnd"
										@click="handleComponentClick(component)"
									>
										<div class="component-icon">{{ component.icon }}</div>
										<div class="component-label">{{ component.label }}</div>
										<div class="component-desc">
											{{ component.description || '' }}
										</div>
									</div>
								</div>
							</el-collapse-transition>
						</div>
					</div>
				</el-tab-pane>

				<!-- 模板 Tab -->
				<el-tab-pane label="模板" name="templates">
					<div class="template-section">
						<div class="template-list">
							<div
								v-for="template in templates"
								:key="template.key"
								class="template-item"
								@click="handleTemplateClick(template)"
							>
								<div class="template-icon">{{ template.icon }}</div>
								<div class="template-content">
									<div class="template-label">{{ template.label }}</div>
									<div class="template-desc">
										{{ template.description || '' }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue';
import { useBuilderStore } from '../../store/builder';
import type { CrudComponentConfig } from '../../types';
import { CrudComponentCategory, FORM_ITEM_COMPONENTS, TABLE_COLUMN_COMPONENTS } from '../../types';

const builderStore = useBuilderStore();

// Tab 状态
const activeTab = ref('components');

// 分类折叠状态
const collapsedCategories = reactive<Record<string, boolean>>({});

// 切换分类折叠状态
const toggleCategory = (categoryKey: string) => {
	collapsedCategories[categoryKey] = !collapsedCategories[categoryKey];
};

// 快速模板
const templates = [
	{
		key: 'basic-crud',
		label: '基础CRUD',
		icon: '📋',
		description: '包含基本的增删改查功能，适合简单的数据管理场景'
	},
	{
		key: 'advanced-crud',
		label: '高级CRUD',
		icon: '🚀',
		description: '包含高级搜索、筛选、导入导出等功能，适合复杂的业务场景'
	},
	{
		key: 'form-only',
		label: '纯表单',
		icon: '📝',
		description: '只包含表单组件，适合数据录入和编辑场景'
	},
	{
		key: 'table-only',
		label: '纯表格',
		icon: '📊',
		description: '只包含表格组件，适合数据展示和查看场景'
	}
];

// CRUD 组件分类配置
const categories = [
	{
		key: CrudComponentCategory.CRUD,
		label: 'CRUD容器',
		icon: '🗂️'
	},
	{
		key: CrudComponentCategory.TABLE,
		label: '表格组件',
		icon: '📊'
	},
	{
		key: CrudComponentCategory.TABLE_COLUMN,
		label: '表格列',
		icon: '📋'
	},
	{
		key: CrudComponentCategory.FORM,
		label: '表单组件',
		icon: '📝'
	},
	{
		key: CrudComponentCategory.FORM_ITEM,
		label: '表单项',
		icon: '📄'
	},
	{
		key: CrudComponentCategory.SEARCH,
		label: '搜索组件',
		icon: '🔍'
	},
	{
		key: CrudComponentCategory.ACTION,
		label: '操作按钮',
		icon: '🔘'
	},
	{
		key: CrudComponentCategory.LAYOUT,
		label: '布局组件',
		icon: '📏'
	}
];

// 根据分类获取组件
const getComponentsByCategory = (category: string) => {
	if (category === CrudComponentCategory.FORM_ITEM) {
		return FORM_ITEM_COMPONENTS;
	} else if (category === CrudComponentCategory.TABLE_COLUMN) {
		return TABLE_COLUMN_COMPONENTS;
	} else {
		return builderStore.availableComponents.filter(component => {
			return component.category === category;
		});
	}
};

// 模板点击
const handleTemplateClick = (template: any) => {
	// 清空当前页面
	builderStore.clearPage();

	if (template.key === 'basic-crud') {
		// 基础CRUD模板
		const crudComponent = builderStore.availableComponents.find(c => c.id === 'cl-crud');
		const tableComponent = builderStore.availableComponents.find(c => c.id === 'cl-table');
		const upsertComponent = builderStore.availableComponents.find(c => c.id === 'cl-upsert');

		if (crudComponent) builderStore.addComponent(crudComponent);
		if (tableComponent) builderStore.addComponent(tableComponent);
		if (upsertComponent) builderStore.addComponent(upsertComponent);

		// 添加操作按钮
		[
			'cl-refresh-btn',
			'cl-add-btn',
			'cl-multi-delete-btn',
			'cl-search-key',
			'cl-pagination'
		].forEach(id => {
			const comp = builderStore.availableComponents.find(c => c.id === id);
			if (comp) builderStore.addComponent(comp);
		});
	} else if (template.key === 'advanced-crud') {
		// 高级CRUD模板
		const crudComponent = builderStore.availableComponents.find(c => c.id === 'cl-crud');
		const tableComponent = builderStore.availableComponents.find(c => c.id === 'cl-table');
		const upsertComponent = builderStore.availableComponents.find(c => c.id === 'cl-upsert');
		const advSearchComponent = builderStore.availableComponents.find(
			c => c.id === 'cl-adv-search'
		);

		if (crudComponent) builderStore.addComponent(crudComponent);
		if (tableComponent) {
			const tableInstance = {
				...tableComponent,
				defaultProps: { ...tableComponent.defaultProps, showSummary: true }
			};
			builderStore.addComponent(tableInstance);
		}
		if (upsertComponent) builderStore.addComponent(upsertComponent);
		if (advSearchComponent) builderStore.addComponent(advSearchComponent);

		// 添加所有操作按钮
		[
			'cl-refresh-btn',
			'cl-add-btn',
			'cl-multi-delete-btn',
			'cl-search-key',
			'cl-adv-btn',
			'cl-filter',
			'cl-import-btn',
			'cl-export-btn',
			'cl-pagination'
		].forEach(id => {
			const comp = builderStore.availableComponents.find(c => c.id === id);
			if (comp) builderStore.addComponent(comp);
		});
	} else if (template.key === 'form-only') {
		// 纯表单模板
		const upsertComponent = builderStore.availableComponents.find(c => c.id === 'cl-upsert');
		if (upsertComponent) builderStore.addComponent(upsertComponent);
	} else if (template.key === 'table-only') {
		// 纯表格模板
		const tableComponent = builderStore.availableComponents.find(c => c.id === 'cl-table');
		if (tableComponent) builderStore.addComponent(tableComponent);

		// 添加分页组件
		const paginationComponent = builderStore.availableComponents.find(
			c => c.id === 'cl-pagination'
		);
		if (paginationComponent) builderStore.addComponent(paginationComponent);
	}

	ElMessage.success(`已应用${template.label}模板`);
};

// 拖拽开始
const handleDragStart = (component: CrudComponentConfig, event: DragEvent) => {
	if (event.dataTransfer) {
		event.dataTransfer.setData(
			'application/json',
			JSON.stringify({
				type: 'component',
				data: component
			})
		);
		event.dataTransfer.effectAllowed = 'copy';
	}
};

// 拖拽结束
const handleDragEnd = () => {
	// 清理拖拽状态
};

// 点击组件
const handleComponentClick = (component: CrudComponentConfig) => {
	// 所有组件都作为独立组件添加到画布
	builderStore.addComponent(component);
	ElMessage.success(`已添加 ${component.label} 组件`);
};
</script>

<style scoped lang="scss">
.component-panel {
	width: 280px;
	height: 100%;
	background: #fff;
	border-right: 1px solid #e4e7ed;
	display: flex;
	flex-direction: column;

	.panel-header {
		padding: 16px;
		border-bottom: 1px solid #e4e7ed;
		background: #f8f9fa;

		h3 {
			margin: 0 0 8px 0;
			font-size: 16px;
			font-weight: 600;
			color: #303133;
		}

		.panel-desc {
			margin: 0;
			font-size: 12px;
			color: #909399;
		}
	}

	.panel-content {
		flex: 1;
		overflow: hidden;
		display: flex;
		flex-direction: column;

		.panel-tabs {
			flex: 1;
			display: flex;
			flex-direction: column;

			:deep(.el-tabs__header) {
				margin: 0;
				padding: 0 16px;
				border-bottom: 1px solid #e4e7ed;
			}

			:deep(.el-tabs__content) {
				flex: 1;
				overflow-y: auto;
				padding: 16px;
			}

			:deep(.el-tab-pane) {
				height: 100%;
			}
		}

		.template-section {
			.template-list {
				display: grid;
				grid-template-columns: 1fr;
				gap: 12px;

				.template-item {
					display: flex;
					align-items: flex-start;
					gap: 12px;
					padding: 16px;
					border: 1px solid #e4e7ed;
					border-radius: 8px;
					cursor: pointer;
					transition: all 0.2s;
					background: #fff;

					&:hover {
						border-color: #409eff;
						box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
						transform: translateY(-2px);
					}

					.template-icon {
						font-size: 24px;
						color: #409eff;
						flex-shrink: 0;
					}

					.template-content {
						flex: 1;

						.template-label {
							font-size: 14px;
							font-weight: 600;
							color: #303133;
							margin-bottom: 4px;
						}

						.template-desc {
							font-size: 12px;
							color: #909399;
							line-height: 1.4;
						}
					}
				}
			}
		}

		.component-categories {
			.category-section {
				margin-bottom: 16px;

				.category-title {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 8px 12px;
					font-size: 14px;
					font-weight: 600;
					color: #606266;
					background: #f8f9fa;
					border-radius: 6px;
					cursor: pointer;
					transition: all 0.2s;
					user-select: none;

					&:hover {
						background: #e9ecef;
						color: #409eff;
					}

					.category-icon {
						font-size: 16px;
					}

					.category-label {
						flex: 1;
						margin-left: 8px;
					}

					.category-toggle {
						color: #909399;
						transition: transform 0.2s;

						.el-icon {
							font-size: 14px;
						}
					}
				}

				.component-list {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					gap: 8px;
					margin-top: 12px;
					padding: 0 4px;

					.component-item {
						display: flex;
						flex-direction: column;
						align-items: center;
						padding: 12px 8px;
						border: 1px solid #e4e7ed;
						border-radius: 6px;
						cursor: grab;
						transition: all 0.2s;
						background: #fff;

						&:hover {
							border-color: #409eff;
							box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
							transform: translateY(-1px);
						}

						&:active {
							cursor: grabbing;
							transform: translateY(0);
						}

						.component-icon {
							margin-bottom: 6px;
							color: #409eff;
							font-size: 20px;

							.el-icon {
								font-size: 24px;
							}
						}

						.component-label {
							font-size: 12px;
							color: #606266;
							text-align: center;
							line-height: 1.2;
							margin-bottom: 4px;
							font-weight: 500;
						}

						.component-desc {
							font-size: 10px;
							color: #909399;
							text-align: center;
							line-height: 1.2;
							max-height: 24px;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}
					}
				}
			}
		}
	}
}

// 滚动条样式
.panel-content::-webkit-scrollbar {
	width: 6px;
}

.panel-content::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;

	&:hover {
		background: #a8a8a8;
	}
}
</style>
