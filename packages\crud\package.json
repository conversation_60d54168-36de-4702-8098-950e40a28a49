{"name": "@cool-vue/crud", "version": "8.0.4", "private": false, "main": "./dist/index.umd.js", "module": "./dist/index.es.js", "types": "types/entry.d.ts", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@vue/runtime-core": "^3.5.13", "element-plus": "^2.9.4", "lodash-es": "^4.17.21", "vue": "^3.5.13"}, "devDependencies": {"@types/node": "^20.11.16", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "prettier": "^3.5.1", "sass": "^1.85.0", "sass-loader": "^16.0.5", "typescript": "^5.3.3", "vite": "^6.1.0", "vite-plugin-dts": "^4.5.0", "vue-tsc": "^2.2.2"}, "files": ["types", "dist", "index.d.ts", "index.ts"]}