<template>
	<div class="crud-renderer">
		<!-- CRUD容器 -->
		<div v-if="component.componentId === 'cl-crud'" class="crud-container">
			<div class="crud-header">
				<span class="crud-title">CRUD容器</span>
				<span class="crud-service">服务: {{ component.props.service || 'test' }}</span>
			</div>
			<div class="crud-content">
				<slot />
			</div>
		</div>

		<!-- 数据表格 -->
		<div v-else-if="component.componentId === 'cl-table'" class="table-container">
			<div class="table-header">
				<span class="table-title">数据表格</span>
				<span class="table-info">{{ columns.length }} 列</span>
			</div>
			<div class="mock-table">
				<table class="table">
					<thead>
						<tr>
							<th
								v-for="(column, index) in columns"
								:key="index"
								:style="getColumnStyle(column)"
							>
								<div class="column-header">
									<span v-if="column.type === 'selection'">
										<el-checkbox :model-value="false" disabled />
									</span>
									<span v-else-if="column.type === 'index'">#</span>
									<span v-else-if="column.type === 'expand'">展开</span>
									<span v-else-if="column.type === 'op'">操作</span>
									<span v-else>{{ column.label || column.prop }}</span>
								</div>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="i in 3" :key="i" class="mock-row">
							<td
								v-for="(column, index) in columns"
								:key="index"
								:style="getColumnStyle(column)"
							>
								<div class="cell-content">
									<span v-if="column.type === 'selection'">
										<el-checkbox :model-value="false" disabled />
									</span>
									<span v-else-if="column.type === 'index'">{{ i }}</span>
									<span v-else-if="column.type === 'expand'">
										<el-button size="small" text>+</el-button>
									</span>
									<span v-else-if="column.type === 'op'">
										<el-button
											v-for="btn in column.buttons || ['edit', 'delete']"
											:key="btn"
											size="small"
											text
										>
											{{ getButtonText(btn) }}
										</el-button>
									</span>
									<span v-else>{{ getMockData(column, i) }}</span>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<!-- 新增编辑表单 -->
		<div v-else-if="component.componentId === 'cl-upsert'" class="upsert-container">
			<div class="upsert-header">
				<span class="upsert-title">新增编辑表单</span>
				<span class="upsert-info">{{ formItems.length }} 个表单项</span>
			</div>
			<div class="mock-form">
				<div v-for="(item, index) in formItems" :key="index" class="form-item">
					<label class="form-label">
						{{ item.label }}
						<span v-if="item.required" class="required">*</span>
					</label>
					<div class="form-control">
						<component :is="getFormComponent(item)" v-bind="getFormProps(item)" />
					</div>
				</div>
			</div>
		</div>

		<!-- 高级搜索 -->
		<div v-else-if="component.componentId === 'cl-adv-search'" class="search-container">
			<div class="search-header">
				<span class="search-title">高级搜索</span>
				<span class="search-info">{{ searchItems.length }} 个搜索项</span>
			</div>
			<div class="mock-search">
				<div v-for="(item, index) in searchItems" :key="index" class="search-item">
					<label class="search-label">{{ item.label }}</label>
					<div class="search-control">
						<component :is="getFormComponent(item)" v-bind="getFormProps(item)" />
					</div>
				</div>
			</div>
		</div>

		<!-- 操作按钮 -->
		<div v-else-if="isActionButton()" class="action-button">
			<el-button :type="getButtonType()" size="default" disabled>
				{{ getButtonIcon() }} {{ component.props.text || component.label }}
			</el-button>
		</div>

		<!-- 关键字搜索 -->
		<div v-else-if="component.componentId === 'cl-search-key'" class="search-key">
			<el-input
				:placeholder="component.props.placeholder || '搜索关键字'"
				:style="{ width: (component.props.width || 250) + 'px' }"
				disabled
			>
				<template #suffix>
					<el-icon><search /></el-icon>
				</template>
			</el-input>
		</div>

		<!-- 筛选器 -->
		<div v-else-if="component.componentId === 'cl-filter'" class="filter-container">
			<el-button disabled>
				{{ component.props.label || '筛选' }}
				<el-icon><arrow-down /></el-icon>
			</el-button>
		</div>

		<!-- 行布局 -->
		<div
			v-else-if="component.componentId === 'cl-row'"
			class="row-layout"
			:style="getRowStyle()"
		>
			<slot />
		</div>

		<!-- 弹性占位 -->
		<div v-else-if="component.componentId === 'cl-flex1'" class="flex-spacer">
			<span class="flex-indicator">弹性占位</span>
		</div>

		<!-- 分页组件 -->
		<div v-else-if="component.componentId === 'cl-pagination'" class="pagination-container">
			<el-pagination
				:current-page="1"
				:page-size="10"
				:total="100"
				:layout="component.props.layout || 'total, sizes, prev, pager, next, jumper'"
				disabled
			/>
		</div>

		<!-- 表单项组件 -->
		<div v-else-if="isFormItem()" class="form-item-component">
			<div class="form-item-preview">
				<label class="form-label">
					{{ component.props.label || component.label }}
					<span v-if="component.props.required" class="required">*</span>
				</label>
				<div class="form-control">
					<component :is="getFormComponentForItem()" v-bind="getFormPropsForItem()" />
				</div>
			</div>
		</div>

		<!-- 表格列组件 -->
		<div v-else-if="isTableColumn()" class="table-column-component">
			<div class="column-preview">
				<div class="column-header">
					<span class="column-title">{{ component.props.label || component.label }}</span>
					<span class="column-type">{{ getColumnTypeText() }}</span>
				</div>
				<div class="column-sample">
					{{ getColumnSampleData() }}
				</div>
			</div>
		</div>

		<!-- 默认渲染 -->
		<div v-else class="default-component">
			<div class="component-placeholder">
				<span class="component-icon">📦</span>
				<span class="component-name">{{ component.label }}</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Search, ArrowDown } from '@element-plus/icons-vue';
import type { CrudComponentInstance, TableColumn, FormItem } from '../../types';

interface Props {
	component: CrudComponentInstance;
}

const props = defineProps<Props>();

// 计算表格列
const columns = computed(() => {
	return (props.component.props.columns as TableColumn[]) || [];
});

// 计算表单项
const formItems = computed(() => {
	return (props.component.props.items as FormItem[]) || [];
});

// 计算搜索项
const searchItems = computed(() => {
	return (props.component.props.items as FormItem[]) || [];
});

// 获取列样式
const getColumnStyle = (column: TableColumn) => {
	const style: any = {};
	if (column.width) {
		style.width = column.width + 'px';
	} else if (column.minWidth) {
		style.minWidth = column.minWidth + 'px';
	}
	if (column.align) {
		style.textAlign = column.align;
	}
	return style;
};

// 获取按钮文本
const getButtonText = (btn: string) => {
	const textMap: Record<string, string> = {
		edit: '编辑',
		delete: '删除',
		info: '详情',
		view: '查看'
	};
	return textMap[btn] || btn;
};

// 获取模拟数据
const getMockData = (column: TableColumn, row: number) => {
	if (column.prop === 'name') return `用户${row}`;
	if (column.prop === 'phone') return `1380000000${row}`;
	if (column.prop === 'email') return `user${row}@example.com`;
	if (column.prop === 'createTime') return '2024-01-01 12:00:00';
	if (column.prop === 'status') return row % 2 ? '启用' : '禁用';
	return `数据${row}`;
};

// 判断是否为操作按钮
const isActionButton = () => {
	return [
		'cl-add-btn',
		'cl-refresh-btn',
		'cl-multi-delete-btn',
		'cl-import-btn',
		'cl-export-btn',
		'cl-adv-btn'
	].includes(props.component.componentId);
};

// 获取按钮类型
const getButtonType = (): any => {
	const typeMap: Record<string, string> = {
		'cl-add-btn': 'primary',
		'cl-refresh-btn': 'default',
		'cl-multi-delete-btn': 'danger',
		'cl-import-btn': 'success',
		'cl-export-btn': 'warning',
		'cl-adv-btn': 'info'
	};
	return typeMap[props.component.componentId] || 'default';
};

// 获取按钮图标
const getButtonIcon = () => {
	const iconMap: Record<string, string> = {
		'cl-add-btn': '➕',
		'cl-refresh-btn': '🔄',
		'cl-multi-delete-btn': '🗑️',
		'cl-import-btn': '📥',
		'cl-export-btn': '📤',
		'cl-adv-btn': '🔧'
	};
	return iconMap[props.component.componentId] || '';
};

// 判断是否为表单项组件
const isFormItem = () => {
	return props.component.componentId.startsWith('form-item-');
};

// 判断是否为表格列组件
const isTableColumn = () => {
	return props.component.componentId.startsWith('table-column-');
};

// 获取列类型文本
const getColumnTypeText = () => {
	const typeMap: Record<string, string> = {
		'table-column-text': '文本',
		'table-column-number': '数字',
		'table-column-date': '日期',
		'table-column-status': '状态'
	};
	return typeMap[props.component.componentId] || '文本';
};

// 获取列示例数据
const getColumnSampleData = () => {
	const sampleMap: Record<string, string> = {
		'table-column-text': '示例文本',
		'table-column-number': '123',
		'table-column-date': '2024-01-01',
		'table-column-status': '启用'
	};
	return sampleMap[props.component.componentId] || '示例数据';
};

// 获取表单项的组件类型
const getFormComponentForItem = () => {
	const componentMap: Record<string, string> = {
		'form-item-input': 'el-input',
		'form-item-number': 'el-input-number',
		'form-item-select': 'el-select',
		'form-item-date': 'el-date-picker',
		'form-item-textarea': 'el-input',
		'form-item-switch': 'el-switch'
	};
	return componentMap[props.component.componentId] || 'el-input';
};

// 获取表单项的属性
const getFormPropsForItem = () => {
	const baseProps: any = { disabled: true };

	switch (props.component.componentId) {
		case 'form-item-textarea':
			return { ...baseProps, type: 'textarea', rows: 3 };
		case 'form-item-date':
			return { ...baseProps, type: 'date', placeholder: '选择日期' };
		case 'form-item-select':
			return { ...baseProps, placeholder: '请选择' };
		default:
			return { ...baseProps, placeholder: '请输入' };
	}
};

// 获取表单组件
const getFormComponent = (item: FormItem) => {
	const componentMap: Record<string, string> = {
		'el-input': 'el-input',
		'el-input-number': 'el-input-number',
		'el-select': 'el-select',
		'el-date-picker': 'el-date-picker',
		'el-switch': 'el-switch',
		'el-radio-group': 'el-radio-group',
		'el-checkbox-group': 'el-checkbox-group'
	};
	return componentMap[item.component.name] || 'el-input';
};

// 获取表单属性
const getFormProps = (item: FormItem) => {
	const props: any = { ...item.component.props, disabled: true };

	if (item.component.name === 'el-input' && props.type === 'textarea') {
		props.rows = 3;
	}

	return props;
};

// 获取行样式
const getRowStyle = () => {
	const style: any = {
		display: 'flex',
		alignItems: 'center',
		gap: '12px',
		marginBottom: '12px'
	};

	if (props.component.props.gutter) {
		style.gap = props.component.props.gutter + 'px';
	}

	return style;
};
</script>

<style scoped lang="scss">
.crud-renderer {
	width: 100%;

	.crud-container {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		background: #fff;

		.crud-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 16px;
			background: #f8f9fa;
			border-bottom: 1px solid #e4e7ed;

			.crud-title {
				font-weight: 500;
				color: #303133;
			}

			.crud-service {
				font-size: 12px;
				color: #909399;
			}
		}

		.crud-content {
			padding: 16px;
		}
	}

	.table-container {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		background: #fff;

		.table-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 16px;
			background: #f8f9fa;
			border-bottom: 1px solid #e4e7ed;

			.table-title {
				font-weight: 500;
				color: #303133;
			}

			.table-info {
				font-size: 12px;
				color: #909399;
			}
		}

		.mock-table {
			.table {
				width: 100%;
				border-collapse: collapse;

				th,
				td {
					padding: 8px 12px;
					border-bottom: 1px solid #ebeef5;
					text-align: left;

					.column-header {
						font-weight: 500;
						color: #909399;
					}

					.cell-content {
						display: flex;
						align-items: center;
						gap: 8px;
					}
				}

				th {
					background: #fafafa;
				}

				.mock-row:hover {
					background: #f5f7fa;
				}
			}
		}
	}

	.upsert-container,
	.search-container {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		background: #fff;

		.upsert-header,
		.search-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 16px;
			background: #f8f9fa;
			border-bottom: 1px solid #e4e7ed;

			.upsert-title,
			.search-title {
				font-weight: 500;
				color: #303133;
			}

			.upsert-info,
			.search-info {
				font-size: 12px;
				color: #909399;
			}
		}

		.mock-form,
		.mock-search {
			padding: 16px;
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 16px;

			.form-item,
			.search-item {
				.form-label,
				.search-label {
					display: block;
					margin-bottom: 4px;
					font-size: 14px;
					color: #606266;

					.required {
						color: #f56c6c;
					}
				}

				.form-control,
				.search-control {
					width: 100%;
				}
			}
		}
	}

	.action-button {
		display: inline-block;
		margin-right: 8px;
	}

	.search-key {
		display: inline-block;
	}

	.filter-container {
		display: inline-block;
		margin-right: 8px;
	}

	.row-layout {
		width: 100%;
		min-height: 40px;
		border: 1px dashed #e4e7ed;
		border-radius: 4px;
		padding: 8px;
	}

	.flex-spacer {
		flex: 1;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px dashed #e4e7ed;
		border-radius: 4px;
		background: #fafafa;

		.flex-indicator {
			font-size: 12px;
			color: #909399;
		}
	}

	.pagination-container {
		display: flex;
		justify-content: center;
		padding: 16px;
	}

	.form-item-component {
		margin-bottom: 16px;

		.form-item-preview {
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			padding: 16px;
			background: #fff;

			.form-label {
				display: block;
				margin-bottom: 8px;
				font-size: 14px;
				color: #606266;
				font-weight: 500;

				.required {
					color: #f56c6c;
					margin-left: 2px;
				}
			}

			.form-control {
				width: 100%;
			}
		}
	}

	.table-column-component {
		margin-bottom: 8px;

		.column-preview {
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			background: #fff;
			overflow: hidden;

			.column-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background: #f8f9fa;
				border-bottom: 1px solid #e4e7ed;

				.column-title {
					font-size: 14px;
					font-weight: 500;
					color: #303133;
				}

				.column-type {
					font-size: 12px;
					color: #909399;
					background: #e4e7ed;
					padding: 2px 6px;
					border-radius: 2px;
				}
			}

			.column-sample {
				padding: 8px 12px;
				font-size: 14px;
				color: #606266;
			}
		}
	}

	.default-component {
		.component-placeholder {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 12px 16px;
			border: 1px dashed #e4e7ed;
			border-radius: 4px;
			background: #fafafa;

			.component-icon {
				font-size: 16px;
			}

			.component-name {
				font-size: 14px;
				color: #606266;
			}
		}
	}
}
</style>
