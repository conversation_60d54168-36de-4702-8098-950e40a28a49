<template>
	<div class="test-generated-page">
		<div class="page-header">
			<h2>生成代码测试页面</h2>
			<p>这里展示低代码系统生成的CRUD代码效果</p>
		</div>

		<div class="test-content">
			<!-- 这里将显示生成的CRUD组件 -->
			<cl-crud ref="Crud">
				<cl-row>
					<cl-refresh-btn />
					<cl-add-btn />
					<cl-multi-delete-btn />
					<cl-flex1 />
					<cl-search-key />
				</cl-row>

				<cl-row>
					<cl-table ref="Table" />
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<cl-pagination />
				</cl-row>

				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert" />
			</cl-crud>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { ref } from 'vue';
import { useDict } from '/$/dict';
import { useCool } from '/@/cool';

const { service } = useCool();
const { dict } = useDict();

// cl-crud 配置
const Crud = useCrud(
	{
		service: 'test'
	},
	app => {
		app.refresh({
			size: 10
		});
	}
);

// cl-table 配置
const Table = useTable({
	autoHeight: false,
	contextMenu: ['refresh'],

	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '姓名',
			prop: 'name',
			minWidth: 140
		},
		{
			label: '手机号',
			prop: 'phone',
			minWidth: 140
		},
		{
			label: '创建时间',
			prop: 'createTime',
			minWidth: 170,
			sortable: 'desc'
		},
		{
			type: 'op',
			buttons: ['edit', 'delete']
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '姓名',
			prop: 'name',
			component: {
				name: 'el-input'
			},
			required: true
		},
		{
			label: '手机号',
			prop: 'phone',
			component: {
				name: 'el-input'
			}
		}
	]
});

// 刷新列表
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>

<style scoped lang="scss">
.test-generated-page {
	padding: 20px;

	.page-header {
		margin-bottom: 20px;
		padding-bottom: 16px;
		border-bottom: 1px solid #e4e7ed;

		h2 {
			margin: 0 0 8px 0;
			color: #303133;
		}

		p {
			margin: 0;
			color: #606266;
			font-size: 14px;
		}
	}

	.test-content {
		background: #fff;
		border-radius: 4px;
		padding: 20px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}
}
</style>
