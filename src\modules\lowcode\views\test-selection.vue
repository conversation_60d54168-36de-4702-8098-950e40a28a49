<template>
	<div class="test-selection">
		<h2>组件选择测试页面</h2>
		<p>用于测试组件选择框显示问题</p>

		<div class="test-info">
			<p><strong>当前选中组件:</strong> {{ selectedComponent?.label || '无' }}</p>
			<p><strong>组件ID:</strong> {{ selectedComponent?.id || '无' }}</p>
		</div>

		<div class="test-actions">
			<el-button @click="addTestComponent">添加测试组件</el-button>
			<el-button
				type="danger"
				:disabled="builderStore.currentPage.components.length === 0"
				@click="clearComponents"
			>
				清空组件
			</el-button>
		</div>

		<!-- 简化的画布区域 -->
		<div class="test-canvas">
			<div v-if="builderStore.currentPage.components.length === 0" class="empty-state">
				点击"添加测试组件"来添加组件进行测试
			</div>

			<component-renderer
				v-for="component in builderStore.currentPage.components"
				:key="component.id"
				:component="component"
				:is-preview="false"
				@select="handleSelectComponent"
				@delete="handleDeleteComponent"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useBuilderStore } from '../store/builder';
import ComponentRenderer from '../components/canvas-panel/component-renderer.vue';
import { CRUD_BUILTIN_COMPONENTS } from '../types';

defineOptions({
	name: 'test-selection'
});

const builderStore = useBuilderStore();

const selectedComponent = computed(() => builderStore.selectedComponent);

const addTestComponent = () => {
	// 添加一个简单的按钮组件进行测试
	const buttonComponent = CRUD_BUILTIN_COMPONENTS.find(c => c.id === 'cl-add-btn');
	if (buttonComponent) {
		builderStore.addComponent(buttonComponent);
	}
};

const clearComponents = () => {
	ElMessageBox.confirm('确定要清空所有组件吗？此操作不可恢复。', '确认清空', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			builderStore.clearPage();
			ElMessage.success('组件已清空');
		})
		.catch(() => {
			// 用户取消操作
		});
};

const handleSelectComponent = (component: any) => {
	console.log('Test page: selecting component', component);
	builderStore.selectComponent(component);
};

const handleDeleteComponent = (component: any) => {
	console.log('Test page: deleting component', component);
	builderStore.removeComponent(component.id);
};
</script>

<style scoped lang="scss">
.test-selection {
	padding: 20px;
	max-width: 1200px;
	margin: 0 auto;

	h2 {
		color: #303133;
		margin-bottom: 10px;
	}

	.test-info {
		background: #f5f7fa;
		padding: 15px;
		border-radius: 4px;
		margin: 20px 0;

		p {
			margin: 5px 0;
			font-size: 14px;
		}
	}

	.test-actions {
		margin: 20px 0;

		.el-button {
			margin-right: 10px;
		}
	}

	.test-canvas {
		border: 2px dashed #e4e7ed;
		border-radius: 4px;
		min-height: 300px;
		padding: 20px;
		background: #fafafa;
		position: relative;

		.empty-state {
			text-align: center;
			color: #909399;
			font-size: 14px;
			padding: 50px 0;
		}
	}
}
</style>
