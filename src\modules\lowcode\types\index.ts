// 基于 @cool-vue/crud 的组件配置
export interface CrudComponentConfig {
	id: string;
	name: string;
	label: string;
	icon: string;
	category: CrudComponentCategory;
	crudType: 'crud' | 'table' | 'upsert' | 'search' | 'form' | 'layout' | 'action';
	props: CrudComponentProp[];
	defaultProps: Record<string, any>;
	slots?: string[];
	events?: string[];
	children?: CrudComponentInstance[];
}

// 组件属性定义
export interface CrudComponentProp {
	name: string;
	label: string;
	type:
		| 'string'
		| 'number'
		| 'boolean'
		| 'array'
		| 'object'
		| 'select'
		| 'color'
		| 'date'
		| 'service'
		| 'columns'
		| 'items';
	default?: any;
	options?: Array<{ label: string; value: any }>;
	required?: boolean;
	description?: string;
	editor?: 'input' | 'select' | 'switch' | 'color' | 'json' | 'columns' | 'items' | 'service';
}

// 组件实例
export interface CrudComponentInstance {
	id: string;
	componentId: string;
	label: string;
	props: Record<string, any>;
	style: Record<string, any>;
	children?: CrudComponentInstance[];
	parentId?: string;
	slot?: string;
}

// CRUD 页面配置
export interface CrudPageConfig {
	id?: string;
	name: string;
	title: string;
	description?: string;
	thumbnail?: string;
	service?: string; // 关联的服务
	components: CrudComponentInstance[];
	globalStyle?: Record<string, any>;
	scripts?: string[];
	createTime?: string;
	updateTime?: string;
}

// 编辑器状态
export interface CrudEditorState {
	selectedComponent?: CrudComponentInstance;
	hoveredComponent?: CrudComponentInstance;
	draggedComponent?: CrudComponentConfig | CrudComponentInstance;
	clipboard?: CrudComponentInstance;
	history: CrudPageConfig[];
	historyIndex: number;
	previewMode: boolean;
	scale: number;
}

// 组件分类
export enum CrudComponentCategory {
	CRUD = 'crud', // CRUD 容器组件
	TABLE = 'table', // 表格相关组件
	TABLE_COLUMN = 'table-column', // 表格列组件
	FORM = 'form', // 表单相关组件
	FORM_ITEM = 'form-item', // 表单项组件
	SEARCH = 'search', // 搜索相关组件
	ACTION = 'action', // 操作按钮组件
	LAYOUT = 'layout' // 布局组件
}

// 基于 @cool-vue/crud 的内置组件配置
export const CRUD_BUILTIN_COMPONENTS: CrudComponentConfig[] = [
	// CRUD 容器组件
	{
		id: 'cl-crud',
		name: 'ClCrud',
		label: 'CRUD容器',
		icon: '🗂️',
		category: CrudComponentCategory.CRUD,
		crudType: 'crud',
		props: [
			{
				name: 'service',
				label: '数据服务',
				type: 'service',
				editor: 'service',
				required: true,
				description: '绑定的服务，如：service.demo.goods、service.base.sys.user'
			},
			{
				name: 'onRefresh',
				label: '刷新事件',
				type: 'string',
				editor: 'input',
				description: '刷新事件处理函数'
			},
			{
				name: 'onDelete',
				label: '删除事件',
				type: 'string',
				editor: 'input',
				description: '删除事件处理函数'
			},
			{
				name: 'dict',
				label: '字典配置',
				type: 'object',
				editor: 'json',
				description: '字典配置，包含api和label配置'
			}
		],
		defaultProps: {
			service: 'test'
		},
		children: []
	},

	// 表格组件
	{
		id: 'cl-table',
		name: 'ClTable',
		label: '数据表格',
		icon: '📊',
		category: CrudComponentCategory.TABLE,
		crudType: 'table',
		props: [
			{
				name: 'columns',
				label: '列配置',
				type: 'columns',
				editor: 'columns',
				required: true,
				description: '表格列配置，支持selection、index、expand、op等类型'
			},
			{
				name: 'autoHeight',
				label: '自动高度',
				type: 'boolean',
				default: false,
				editor: 'switch',
				description: '是否自动计算表格高度，弹窗中建议设为false'
			},
			{
				name: 'contextMenu',
				label: '右键菜单',
				type: 'array',
				default: ['refresh'],
				editor: 'json',
				description: '右键菜单配置'
			},
			{
				name: 'showSummary',
				label: '显示合计',
				type: 'boolean',
				default: false,
				editor: 'switch'
			},
			{
				name: 'summaryMethod',
				label: '合计方法',
				type: 'string',
				editor: 'input',
				description: '自定义合计方法名'
			}
		],
		defaultProps: {
			columns: [
				{ type: 'selection', width: 60 },
				{ label: '姓名', prop: 'name', minWidth: 140 },
				{ label: '手机号', prop: 'phone', minWidth: 140 },
				{ label: '创建时间', prop: 'createTime', minWidth: 170, sortable: 'desc' },
				{ type: 'op', buttons: ['edit', 'delete'] }
			],
			autoHeight: false,
			contextMenu: ['refresh']
		}
	},

	// 新增编辑表单组件
	{
		id: 'cl-upsert',
		name: 'ClUpsert',
		label: '新增编辑表单',
		icon: '📝',
		category: CrudComponentCategory.FORM,
		crudType: 'upsert',
		props: [
			{
				name: 'items',
				label: '表单项',
				type: 'items',
				editor: 'items',
				required: true,
				description: '表单项配置，支持各种组件类型'
			},
			{
				name: 'onInfo',
				label: '详情钩子',
				type: 'string',
				editor: 'input',
				description: '详情数据加载钩子函数'
			},
			{
				name: 'onSubmit',
				label: '提交钩子',
				type: 'string',
				editor: 'input',
				description: '表单提交钩子函数'
			},
			{
				name: 'onOpened',
				label: '打开后钩子',
				type: 'string',
				editor: 'input',
				description: '表单打开后钩子函数'
			},
			{
				name: 'onClose',
				label: '关闭钩子',
				type: 'string',
				editor: 'input',
				description: '表单关闭钩子函数'
			}
		],
		defaultProps: {
			items: [
				{ label: '姓名', prop: 'name', component: { name: 'el-input' }, required: true },
				{ label: '手机号', prop: 'phone', component: { name: 'el-input' } }
			]
		}
	},

	// 高级搜索组件
	{
		id: 'cl-adv-search',
		name: 'ClAdvSearch',
		label: '高级搜索',
		icon: '🔍',
		category: CrudComponentCategory.SEARCH,
		crudType: 'search',
		props: [
			{
				name: 'items',
				label: '搜索项',
				type: 'items',
				editor: 'items',
				description: '高级搜索表单项配置'
			}
		],
		defaultProps: {
			items: [
				{
					label: '姓名',
					prop: 'name',
					component: { name: 'el-input', props: { clearable: true } }
				}
			]
		}
	},

	// 操作按钮组件
	{
		id: 'cl-add-btn',
		name: 'ClAddBtn',
		label: '新增按钮',
		icon: '➕',
		category: CrudComponentCategory.ACTION,
		crudType: 'action',
		props: [
			{ name: 'text', label: '按钮文字', type: 'string', default: '新增', editor: 'input' }
		],
		defaultProps: {
			text: '新增'
		}
	},

	{
		id: 'cl-refresh-btn',
		name: 'ClRefreshBtn',
		label: '刷新按钮',
		icon: '🔄',
		category: CrudComponentCategory.ACTION,
		crudType: 'action',
		props: [
			{ name: 'text', label: '按钮文字', type: 'string', default: '刷新', editor: 'input' }
		],
		defaultProps: {
			text: '刷新'
		}
	},

	{
		id: 'cl-multi-delete-btn',
		name: 'ClMultiDeleteBtn',
		label: '批量删除按钮',
		icon: '🗑️',
		category: CrudComponentCategory.ACTION,
		crudType: 'action',
		props: [
			{ name: 'text', label: '按钮文字', type: 'string', default: '删除', editor: 'input' }
		],
		defaultProps: {
			text: '删除'
		}
	},

	{
		id: 'cl-search-key',
		name: 'ClSearchKey',
		label: '关键字搜索',
		icon: '🔎',
		category: CrudComponentCategory.SEARCH,
		crudType: 'search',
		props: [
			{
				name: 'placeholder',
				label: '占位符',
				type: 'string',
				default: '搜索关键字',
				editor: 'input'
			},
			{
				name: 'width',
				label: '宽度',
				type: 'number',
				default: 250,
				editor: 'input'
			}
		],
		defaultProps: {
			placeholder: '搜索关键字',
			width: 250
		}
	},

	{
		id: 'cl-adv-btn',
		name: 'ClAdvBtn',
		label: '高级搜索按钮',
		icon: '🔧',
		category: CrudComponentCategory.SEARCH,
		crudType: 'search',
		props: [],
		defaultProps: {}
	},

	// 布局组件
	{
		id: 'cl-row',
		name: 'ClRow',
		label: '行布局',
		icon: '📏',
		category: CrudComponentCategory.LAYOUT,
		crudType: 'layout',
		props: [
			{
				name: 'gutter',
				label: '间距',
				type: 'number',
				default: 0,
				editor: 'input'
			}
		],
		defaultProps: {
			gutter: 0
		},
		children: []
	},

	{
		id: 'cl-flex1',
		name: 'ClFlex1',
		label: '弹性占位',
		icon: '↔️',
		category: CrudComponentCategory.LAYOUT,
		crudType: 'layout',
		props: [],
		defaultProps: {}
	},

	{
		id: 'cl-pagination',
		name: 'ClPagination',
		label: '分页组件',
		icon: '📄',
		category: CrudComponentCategory.LAYOUT,
		crudType: 'layout',
		props: [
			{
				name: 'layout',
				label: '布局',
				type: 'string',
				default: 'total, sizes, prev, pager, next, jumper',
				editor: 'input'
			}
		],
		defaultProps: {
			layout: 'total, sizes, prev, pager, next, jumper'
		}
	},

	// 筛选组件
	{
		id: 'cl-filter',
		name: 'ClFilter',
		label: '筛选器',
		icon: '🎛️',
		category: CrudComponentCategory.SEARCH,
		crudType: 'search',
		props: [
			{
				name: 'label',
				label: '标签',
				type: 'string',
				required: true,
				editor: 'input'
			}
		],
		defaultProps: {
			label: '筛选'
		},
		children: []
	},

	// 导入导出组件
	{
		id: 'cl-import-btn',
		name: 'ClImportBtn',
		label: '导入按钮',
		icon: '📥',
		category: CrudComponentCategory.ACTION,
		crudType: 'action',
		props: [
			{
				name: 'template',
				label: '模板文件',
				type: 'string',
				editor: 'input',
				description: '导入模板文件路径'
			}
		],
		defaultProps: {
			template: '/导入模板.xlsx'
		}
	},

	{
		id: 'cl-export-btn',
		name: 'ClExportBtn',
		label: '导出按钮',
		icon: '📤',
		category: CrudComponentCategory.ACTION,
		crudType: 'action',
		props: [
			{
				name: 'columns',
				label: '导出列',
				type: 'string',
				default: 'Table?.columns',
				editor: 'input',
				description: '导出的列配置引用'
			}
		],
		defaultProps: {
			columns: 'Table?.columns'
		}
	}
];

// 表单项组件库 - 作为独立的可拖拽组件
export const FORM_ITEM_COMPONENTS: CrudComponentConfig[] = [
	{
		id: 'form-item-input',
		name: 'FormItemInput',
		label: '输入框',
		icon: '📝',
		category: CrudComponentCategory.FORM_ITEM,
		crudType: 'form',
		props: [
			{ name: 'label', label: '标签', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' },
			{ name: 'required', label: '必填', type: 'boolean', default: false, editor: 'switch' },
			{ name: 'placeholder', label: '占位符', type: 'string', editor: 'input' },
			{ name: 'type', label: '类型', type: 'string', editor: 'select' }
		],
		defaultProps: {
			label: '输入框',
			prop: 'input',
			component: { name: 'el-input', props: { clearable: true } }
		}
	},
	{
		id: 'form-item-number',
		name: 'FormItemNumber',
		label: '数字输入框',
		icon: '🔢',
		category: CrudComponentCategory.FORM_ITEM,
		crudType: 'form',
		props: [
			{ name: 'label', label: '标签', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' },
			{ name: 'required', label: '必填', type: 'boolean', default: false, editor: 'switch' }
		],
		defaultProps: {
			label: '数字输入框',
			prop: 'number',
			component: { name: 'el-input-number', props: {} }
		}
	},
	{
		id: 'form-item-select',
		name: 'FormItemSelect',
		label: '选择器',
		icon: '📋',
		category: CrudComponentCategory.FORM_ITEM,
		crudType: 'form',
		props: [
			{ name: 'label', label: '标签', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' },
			{ name: 'required', label: '必填', type: 'boolean', default: false, editor: 'switch' }
		],
		defaultProps: {
			label: '选择器',
			prop: 'select',
			component: {
				name: 'el-select',
				props: { clearable: true },
				options: [
					{ label: '选项1', value: '1' },
					{ label: '选项2', value: '2' }
				]
			}
		}
	},
	{
		id: 'form-item-date',
		name: 'FormItemDate',
		label: '日期选择器',
		icon: '📅',
		category: CrudComponentCategory.FORM_ITEM,
		crudType: 'form',
		props: [
			{ name: 'label', label: '标签', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' },
			{ name: 'required', label: '必填', type: 'boolean', default: false, editor: 'switch' }
		],
		defaultProps: {
			label: '日期选择器',
			prop: 'date',
			component: {
				name: 'el-date-picker',
				props: { type: 'date', valueFormat: 'YYYY-MM-DD' }
			}
		}
	},
	{
		id: 'form-item-textarea',
		name: 'FormItemTextarea',
		label: '文本域',
		icon: '📄',
		category: CrudComponentCategory.FORM_ITEM,
		crudType: 'form',
		props: [
			{ name: 'label', label: '标签', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' },
			{ name: 'required', label: '必填', type: 'boolean', default: false, editor: 'switch' }
		],
		defaultProps: {
			label: '文本域',
			prop: 'textarea',
			component: { name: 'el-input', props: { type: 'textarea', rows: 3 } }
		}
	},
	{
		id: 'form-item-switch',
		name: 'FormItemSwitch',
		label: '开关',
		icon: '🔘',
		category: CrudComponentCategory.FORM_ITEM,
		crudType: 'form',
		props: [
			{ name: 'label', label: '标签', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' }
		],
		defaultProps: {
			label: '开关',
			prop: 'switch',
			component: { name: 'el-switch', props: {} }
		}
	}
];

// 表格列组件库 - 作为独立的可拖拽组件
export const TABLE_COLUMN_COMPONENTS: CrudComponentConfig[] = [
	{
		id: 'table-column-text',
		name: 'TableColumnText',
		label: '文本列',
		icon: '📝',
		category: CrudComponentCategory.TABLE_COLUMN,
		crudType: 'table',
		props: [
			{ name: 'label', label: '列标题', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' },
			{ name: 'width', label: '列宽', type: 'number', editor: 'input' },
			{ name: 'minWidth', label: '最小宽度', type: 'number', editor: 'input' }
		],
		defaultProps: {
			label: '文本列',
			prop: 'text',
			minWidth: 120
		}
	},
	{
		id: 'table-column-number',
		name: 'TableColumnNumber',
		label: '数字列',
		icon: '🔢',
		category: CrudComponentCategory.TABLE_COLUMN,
		crudType: 'table',
		props: [
			{ name: 'label', label: '列标题', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' }
		],
		defaultProps: {
			label: '数字列',
			prop: 'number',
			minWidth: 100,
			align: 'right'
		}
	},
	{
		id: 'table-column-date',
		name: 'TableColumnDate',
		label: '日期列',
		icon: '📅',
		category: CrudComponentCategory.TABLE_COLUMN,
		crudType: 'table',
		props: [
			{ name: 'label', label: '列标题', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' }
		],
		defaultProps: {
			label: '日期列',
			prop: 'date',
			minWidth: 160,
			sortable: 'desc'
		}
	},
	{
		id: 'table-column-status',
		name: 'TableColumnStatus',
		label: '状态列',
		icon: '🔘',
		category: CrudComponentCategory.TABLE_COLUMN,
		crudType: 'table',
		props: [
			{ name: 'label', label: '列标题', type: 'string', required: true, editor: 'input' },
			{ name: 'prop', label: '字段名', type: 'string', required: true, editor: 'input' }
		],
		defaultProps: {
			label: '状态列',
			prop: 'status',
			minWidth: 100,
			dict: 'status'
		}
	}
];

// 列配置接口
export interface TableColumn {
	type?: 'selection' | 'index' | 'expand' | 'op';
	prop?: string;
	label?: string;
	width?: number;
	minWidth?: number;
	fixed?: boolean | 'left' | 'right';
	sortable?: boolean | 'custom' | 'asc' | 'desc';
	align?: 'left' | 'center' | 'right';
	headerAlign?: 'left' | 'center' | 'right';
	showOverflowTooltip?: boolean;
	formatter?: string;
	buttons?: string[] | any[];
	dict?: string;
	component?: any;
}

// 表单项配置接口
export interface FormItem {
	label: string;
	prop: string;
	required?: boolean;
	component: {
		name: string;
		props?: Record<string, any>;
		options?: Array<{ label: string; value: any }>;
	};
	group?: string;
	span?: number;
	rules?: any[];
	hidden?: boolean | string;
	flex?: boolean;
	labelWidth?: string;
	hook?: string;
}
