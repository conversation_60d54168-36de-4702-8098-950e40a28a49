<template>
	<div class="test-scroll">
		<h2>组件面板滚动测试</h2>
		<p>用于测试组件面板的滚动功能</p>

		<div class="test-container">
			<!-- 模拟组件面板 -->
			<div class="mock-panel">
				<div class="panel-header">
					<h3>组件面板</h3>
				</div>

				<div class="panel-content">
					<!-- 卡片式 Tab 切换 -->
					<div class="card-tabs">
						<div class="tab-header">
							<div
								class="tab-card"
								:class="{ active: activeTab === 'components' }"
								@click="activeTab = 'components'"
							>
								<div class="tab-icon">
									<el-icon><Grid /></el-icon>
								</div>
								<div class="tab-label">组件</div>
							</div>
							<div
								class="tab-card"
								:class="{ active: activeTab === 'templates' }"
								@click="activeTab = 'templates'"
							>
								<div class="tab-icon">
									<el-icon><Document /></el-icon>
								</div>
								<div class="tab-label">模板</div>
							</div>
						</div>

						<div class="tab-content">
							<!-- 组件内容 -->
							<div v-show="activeTab === 'components'" class="tab-pane">
								<div class="component-categories">
									<div
										v-for="category in mockCategories"
										:key="category.key"
										class="category-section"
									>
										<div
											class="category-title"
											@click="toggleCategory(category.key)"
										>
											<span class="category-icon">
												<Icon :icon="category.icon" />
											</span>
											<span class="category-label">{{ category.label }}</span>
											<span class="category-toggle">
												<el-icon>
													<ArrowDown
														v-if="!collapsedCategories[category.key]"
													/>
													<ArrowRight v-else />
												</el-icon>
											</span>
										</div>

										<el-collapse-transition>
											<div
												v-show="!collapsedCategories[category.key]"
												class="component-list"
											>
												<div
													v-for="component in category.components"
													:key="component.id"
													class="component-item"
												>
													<div class="component-icon">
														<Icon :icon="component.icon" />
													</div>
													<div class="component-label">
														{{ component.label }}
													</div>
													<div class="component-desc">
														{{ component.description }}
													</div>
												</div>
											</div>
										</el-collapse-transition>
									</div>
								</div>
							</div>

							<!-- 模板内容 -->
							<div v-show="activeTab === 'templates'" class="tab-pane">
								<div class="template-section">
									<div class="template-list">
										<div
											v-for="template in mockTemplates"
											:key="template.key"
											class="template-item"
										>
											<div class="template-icon">
												<Icon :icon="template.icon" />
											</div>
											<div class="template-content">
												<div class="template-label">
													{{ template.label }}
												</div>
												<div class="template-desc">
													{{ template.description }}
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="test-info">
				<h4>测试说明</h4>
				<ul>
					<li>左侧面板应该可以正常滚动</li>
					<li>点击分类标题可以折叠/展开</li>
					<li>折叠后面板仍然可以滚动</li>
					<li>切换Tab后滚动功能正常</li>
				</ul>

				<div class="controls">
					<el-button @click="toggleAllCategories(false)">展开所有分类</el-button>
					<el-button @click="toggleAllCategories(true)">折叠所有分类</el-button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ArrowDown, ArrowRight, Grid, Document } from '@element-plus/icons-vue';
import { Icon } from '@iconify/vue';

defineOptions({
	name: 'test-scroll'
});

// Tab 状态
const activeTab = ref('components');

// 分类折叠状态
const collapsedCategories = reactive<Record<string, boolean>>({});

// 切换分类折叠状态
const toggleCategory = (categoryKey: string) => {
	collapsedCategories[categoryKey] = !collapsedCategories[categoryKey];
};

// 切换所有分类
const toggleAllCategories = (collapsed: boolean) => {
	mockCategories.forEach(category => {
		collapsedCategories[category.key] = collapsed;
	});
};

// 模拟数据
const mockCategories = [
	{
		key: 'form',
		label: '表单组件',
		icon: 'mdi:form-select',
		components: Array.from({ length: 10 }, (_, i) => ({
			id: `form-${i}`,
			label: `表单组件 ${i + 1}`,
			icon: 'mdi:form-textbox',
			description: `这是表单组件 ${i + 1} 的描述`
		}))
	},
	{
		key: 'table',
		label: '表格组件',
		icon: 'mdi:table',
		components: Array.from({ length: 8 }, (_, i) => ({
			id: `table-${i}`,
			label: `表格组件 ${i + 1}`,
			icon: 'mdi:table-large',
			description: `这是表格组件 ${i + 1} 的描述`
		}))
	},
	{
		key: 'layout',
		label: '布局组件',
		icon: 'mdi:view-dashboard',
		components: Array.from({ length: 6 }, (_, i) => ({
			id: `layout-${i}`,
			label: `布局组件 ${i + 1}`,
			icon: 'mdi:view-grid',
			description: `这是布局组件 ${i + 1} 的描述`
		}))
	},
	{
		key: 'display',
		label: '展示组件',
		icon: 'mdi:palette',
		components: Array.from({ length: 12 }, (_, i) => ({
			id: `display-${i}`,
			label: `展示组件 ${i + 1}`,
			icon: 'mdi:image',
			description: `这是展示组件 ${i + 1} 的描述`
		}))
	},
	{
		key: 'action',
		label: '操作组件',
		icon: 'mdi:lightning-bolt',
		components: Array.from({ length: 6 }, (_, i) => ({
			id: `action-${i}`,
			label: `操作组件 ${i + 1}`,
			icon: 'mdi:button-cursor',
			description: `这是操作组件 ${i + 1} 的描述`
		}))
	}
];

const mockTemplates = Array.from({ length: 8 }, (_, i) => ({
	key: `template-${i}`,
	label: `模板 ${i + 1}`,
	icon: 'mdi:clipboard-list',
	description: `这是模板 ${i + 1} 的详细描述，包含了多个组件的组合`
}));
</script>

<style scoped lang="scss">
.test-scroll {
	padding: 20px;
	height: 100vh;
	overflow: auto;

	h2 {
		color: #303133;
		margin-bottom: 10px;
	}

	.test-container {
		display: flex;
		gap: 20px;
		height: calc(100vh - 120px);

		.mock-panel {
			width: 280px;
			height: 100%;
			background: #fff;
			border: 1px solid #e4e7ed;
			border-radius: 8px;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.panel-header {
				padding: 16px;
				border-bottom: 1px solid #e4e7ed;
				background: #f8f9fa;

				h3 {
					margin: 0;
					font-size: 16px;
					color: #303133;
				}
			}

			.panel-content {
				flex: 1;
				overflow: hidden;
				display: flex;
				flex-direction: column;

				.card-tabs {
					flex: 1;
					display: flex;
					flex-direction: column;
					height: 100%;

					.tab-header {
						display: flex;
						padding: 12px;
						gap: 8px;
						background: #f8f9fa;
						border-bottom: 1px solid #e4e7ed;
						flex-shrink: 0;

						.tab-card {
							flex: 1;
							display: flex;
							flex-direction: column;
							align-items: center;
							padding: 12px 8px;
							background: #fff;
							border: 1px solid #e4e7ed;
							border-radius: 8px;
							cursor: pointer;
							transition: all 0.3s ease;
							user-select: none;

							&:hover {
								border-color: #409eff;
								box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
								transform: translateY(-1px);
							}

							&.active {
								border-color: #409eff;
								background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
								color: #fff;
								box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
								transform: translateY(-2px);

								.tab-icon {
									transform: scale(1.1);
								}
							}

							.tab-icon {
								font-size: 20px;
								margin-bottom: 4px;
								transition: transform 0.3s ease;
							}

							.tab-label {
								font-size: 12px;
								font-weight: 600;
								text-align: center;
							}
						}
					}

					.tab-content {
						flex: 1;
						overflow-y: auto;
						padding: 16px;
						height: 0;

						.tab-pane {
							height: auto;
							min-height: 100%;
						}
					}
				}

				// 复制组件面板的样式
				.component-categories {
					height: auto;
					overflow: visible;

					.category-section {
						margin-bottom: 16px;

						.category-title {
							display: flex;
							align-items: center;
							justify-content: space-between;
							padding: 8px 12px;
							font-size: 14px;
							font-weight: 600;
							color: #606266;
							background: #f8f9fa;
							border-radius: 6px;
							cursor: pointer;
							transition: all 0.2s;
							user-select: none;

							&:hover {
								background: #e9ecef;
								color: #409eff;
							}

							.category-icon {
								font-size: 16px;
								display: flex;
								align-items: center;

								:deep(.iconify) {
									width: 16px;
									height: 16px;
								}
							}

							.category-label {
								flex: 1;
								margin-left: 8px;
							}

							.category-toggle {
								color: #909399;
								transition: transform 0.2s;

								.el-icon {
									font-size: 14px;
								}
							}
						}

						.component-list {
							display: grid;
							grid-template-columns: repeat(2, 1fr);
							gap: 8px;
							margin-top: 12px;
							padding: 0 4px;
							overflow: visible;

							.component-item {
								display: flex;
								flex-direction: column;
								align-items: center;
								padding: 12px 8px;
								border: 1px solid #e4e7ed;
								border-radius: 6px;
								cursor: pointer;
								transition: all 0.2s;
								background: #fff;

								&:hover {
									border-color: #409eff;
									box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
									transform: translateY(-1px);
								}

								.component-icon {
									margin-bottom: 6px;
									color: #409eff;
									font-size: 20px;
									display: flex;
									align-items: center;
									justify-content: center;

									:deep(.iconify) {
										width: 20px;
										height: 20px;
									}
								}

								.component-label {
									font-size: 12px;
									color: #606266;
									text-align: center;
									line-height: 1.2;
									margin-bottom: 4px;
									font-weight: 500;
								}

								.component-desc {
									font-size: 10px;
									color: #909399;
									text-align: center;
									line-height: 1.2;
									max-height: 24px;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}
							}
						}
					}
				}

				.template-section {
					height: auto;
					overflow: visible;

					.template-list {
						display: grid;
						grid-template-columns: 1fr;
						gap: 12px;

						.template-item {
							display: flex;
							align-items: flex-start;
							gap: 12px;
							padding: 16px;
							border: 1px solid #e4e7ed;
							border-radius: 8px;
							cursor: pointer;
							transition: all 0.2s;
							background: #fff;

							&:hover {
								border-color: #409eff;
								box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
								transform: translateY(-2px);
							}

							.template-icon {
								font-size: 24px;
								color: #409eff;
								flex-shrink: 0;
								display: flex;
								align-items: center;
								justify-content: center;

								:deep(.iconify) {
									width: 24px;
									height: 24px;
								}
							}

							.template-content {
								flex: 1;

								.template-label {
									font-size: 14px;
									font-weight: 600;
									color: #303133;
									margin-bottom: 4px;
								}

								.template-desc {
									font-size: 12px;
									color: #909399;
									line-height: 1.4;
								}
							}
						}
					}
				}
			}
		}

		.test-info {
			flex: 1;
			padding: 20px;
			background: #f8f9fa;
			border-radius: 8px;

			h4 {
				color: #303133;
				margin-bottom: 16px;
			}

			ul {
				margin-bottom: 20px;
				padding-left: 20px;

				li {
					margin-bottom: 8px;
					color: #606266;
				}
			}

			.controls {
				.el-button {
					margin-right: 10px;
				}
			}
		}
	}
}
</style>
