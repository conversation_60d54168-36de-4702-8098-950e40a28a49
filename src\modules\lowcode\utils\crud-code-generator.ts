import type { CrudPageConfig, CrudComponentInstance, TableColumn, FormItem } from '../types';

/**
 * CRUD代码生成器
 * 基于 .cursor/rules/crud.mdc 模板生成标准CRUD代码
 */
export class CrudCodeGenerator {
	private page: CrudPageConfig;
	private crudConfig: any = {};
	private tableConfig: any = {};
	private upsertConfig: any = {};
	private advSearchConfig: any = {};

	constructor(page: CrudPageConfig) {
		this.page = page;
		this.analyzeComponents();
	}

	/**
	 * 分析组件配置
	 */
	private analyzeComponents() {
		this.analyzeComponentsRecursive(this.page.components);
	}

	/**
	 * 递归分析组件配置
	 */
	private analyzeComponentsRecursive(components: CrudComponentInstance[]) {
		components.forEach(component => {
			switch (component.componentId) {
				case 'cl-crud':
					this.crudConfig = component.props;
					break;
				case 'cl-table':
					this.tableConfig = component.props;
					// 收集表格列组件
					this.collectTableColumns(components);
					break;
				case 'cl-upsert':
					this.upsertConfig = component.props;
					// 收集表单项组件
					this.collectFormItems(components);
					break;
				case 'cl-adv-search':
					this.advSearchConfig = component.props;
					// 收集搜索项组件
					this.collectSearchItems(components);
					break;
			}

			// 递归分析子组件
			if (component.children && component.children.length > 0) {
				this.analyzeComponentsRecursive(component.children);
			}
		});
	}

	/**
	 * 收集表格列组件
	 */
	private collectTableColumns(components: CrudComponentInstance[]) {
		const columns: any[] = [];

		// 查找所有表格列组件
		const findTableColumns = (comps: CrudComponentInstance[]) => {
			comps.forEach(comp => {
				if (comp.componentId.startsWith('table-column-')) {
					const column = this.convertTableColumnComponent(comp);
					columns.push(column);
				}
				if (comp.children) {
					findTableColumns(comp.children);
				}
			});
		};

		findTableColumns(components);

		// 如果找到了表格列组件，更新表格配置
		if (columns.length > 0) {
			if (!this.tableConfig.columns) {
				this.tableConfig.columns = [];
			}
			// 添加默认的选择列和操作列
			const finalColumns = [
				{ type: 'selection', width: 60 },
				...columns,
				{ type: 'op', buttons: ['edit', 'delete'] }
			];
			this.tableConfig.columns = finalColumns;
		}
	}

	/**
	 * 收集表单项组件
	 */
	private collectFormItems(components: CrudComponentInstance[]) {
		const items: any[] = [];

		// 查找所有表单项组件
		const findFormItems = (comps: CrudComponentInstance[]) => {
			comps.forEach(comp => {
				if (comp.componentId.startsWith('form-item-')) {
					const item = this.convertFormItemComponent(comp);
					items.push(item);
				}
				if (comp.children) {
					findFormItems(comp.children);
				}
			});
		};

		findFormItems(components);

		// 如果找到了表单项组件，更新表单配置
		if (items.length > 0) {
			this.upsertConfig.items = items;
		}
	}

	/**
	 * 收集搜索项组件
	 */
	private collectSearchItems(components: CrudComponentInstance[]) {
		const items: any[] = [];

		// 查找所有表单项组件（搜索项使用相同的组件）
		const findSearchItems = (comps: CrudComponentInstance[]) => {
			comps.forEach(comp => {
				if (comp.componentId.startsWith('form-item-')) {
					const item = this.convertFormItemComponent(comp);
					items.push(item);
				}
				if (comp.children) {
					findSearchItems(comp.children);
				}
			});
		};

		findSearchItems(components);

		// 如果找到了搜索项组件，更新搜索配置
		if (items.length > 0) {
			this.advSearchConfig.items = items;
		}
	}

	/**
	 * 转换表格列组件为列配置
	 */
	private convertTableColumnComponent(component: CrudComponentInstance): any {
		const typeMap: Record<string, any> = {
			'table-column-text': {
				label: component.props.label || '文本列',
				prop: component.props.prop || 'text',
				minWidth: component.props.minWidth || 120
			},
			'table-column-number': {
				label: component.props.label || '数字列',
				prop: component.props.prop || 'number',
				minWidth: component.props.minWidth || 100,
				align: 'right'
			},
			'table-column-date': {
				label: component.props.label || '日期列',
				prop: component.props.prop || 'date',
				minWidth: component.props.minWidth || 160,
				sortable: 'desc'
			},
			'table-column-status': {
				label: component.props.label || '状态列',
				prop: component.props.prop || 'status',
				minWidth: component.props.minWidth || 100,
				dict: component.props.dict || 'status'
			}
		};

		return (
			typeMap[component.componentId] || {
				label: component.props.label || '列',
				prop: component.props.prop || 'field',
				minWidth: 120
			}
		);
	}

	/**
	 * 转换表单项组件为表单项配置
	 */
	private convertFormItemComponent(component: CrudComponentInstance): any {
		const typeMap: Record<string, any> = {
			'form-item-input': {
				label: component.props.label || '输入框',
				prop: component.props.prop || 'input',
				component: { name: 'el-input', props: { clearable: true } },
				required: component.props.required || false
			},
			'form-item-number': {
				label: component.props.label || '数字输入框',
				prop: component.props.prop || 'number',
				component: { name: 'el-input-number', props: {} },
				required: component.props.required || false
			},
			'form-item-select': {
				label: component.props.label || '选择器',
				prop: component.props.prop || 'select',
				component: {
					name: 'el-select',
					props: { clearable: true },
					options: component.props.options || [
						{ label: '选项1', value: '1' },
						{ label: '选项2', value: '2' }
					]
				},
				required: component.props.required || false
			},
			'form-item-date': {
				label: component.props.label || '日期选择器',
				prop: component.props.prop || 'date',
				component: {
					name: 'el-date-picker',
					props: { type: 'date', valueFormat: 'YYYY-MM-DD' }
				},
				required: component.props.required || false
			},
			'form-item-textarea': {
				label: component.props.label || '文本域',
				prop: component.props.prop || 'textarea',
				component: { name: 'el-input', props: { type: 'textarea', rows: 3 } },
				required: component.props.required || false
			},
			'form-item-switch': {
				label: component.props.label || '开关',
				prop: component.props.prop || 'switch',
				component: { name: 'el-switch', props: {} },
				required: component.props.required || false
			}
		};

		return (
			typeMap[component.componentId] || {
				label: component.props.label || '表单项',
				prop: component.props.prop || 'field',
				component: { name: 'el-input', props: {} },
				required: false
			}
		);
	}

	/**
	 * 生成完整的Vue代码
	 */
	generateVueCode(): string {
		const template = this.generateTemplate();
		const script = this.generateScript();
		const style = this.generateStyle();

		return `<template>
${template}
</template>

<script setup lang="ts">
${script}
<\/script>

<style scoped>
${style}
<\/style>`;
	}

	/**
	 * 生成模板部分
	 */
	private generateTemplate(): string {
		const hasDialog = this.shouldWrapInDialog();

		if (hasDialog) {
			return this.generateDialogTemplate();
		} else {
			return this.generateDirectTemplate();
		}
	}

	/**
	 * 判断是否需要包装在对话框中
	 */
	private shouldWrapInDialog(): boolean {
		// 如果页面标题不是默认值，则包装在对话框中
		return this.page.title !== '未命名CRUD页面';
	}

	/**
	 * 生成对话框模板
	 */
	private generateDialogTemplate(): string {
		return `	<div class="scope">
		<div class="h">
			<el-tag size="small" effect="dark" disable-transitions>${this.page.name || 'demo'}</el-tag>
			<span>${this.page.title}</span>
		</div>

		<div class="c">
			<el-button @click="open">预览</el-button>
			<demo-code :files="['crud/${this.page.name || 'demo'}.vue']" />

			<cl-dialog v-model="visible" title="${this.page.title}" width="80%">
${this.generateCrudContent('				')}
			</cl-dialog>
		</div>

		<div class="f">
			<span class="date">${new Date().toISOString().split('T')[0]}</span>
		</div>
	</div>`;
	}

	/**
	 * 生成直接模板
	 */
	private generateDirectTemplate(): string {
		return this.generateCrudContent('	');
	}

	/**
	 * 生成CRUD内容
	 */
	private generateCrudContent(indent: string): string {
		const rows = this.generateRows(indent + '\t');

		return `${indent}<cl-crud ref="Crud">
${rows}

${indent}\t<!-- 新增、编辑 -->
${indent}\t<cl-upsert ref="Upsert" />

${this.hasAdvSearch() ? `${indent}\t<!-- 高级搜索 -->\n${indent}\t<cl-adv-search ref="AdvSearch" />\n` : ''}${indent}</cl-crud>`;
	}

	/**
	 * 生成行内容
	 */
	private generateRows(indent: string): string {
		const rows: string[] = [];

		// 第一行：操作按钮
		const firstRowComponents = this.getFirstRowComponents();
		if (firstRowComponents.length > 0) {
			rows.push(`${indent}<cl-row>`);
			firstRowComponents.forEach(comp => {
				rows.push(`${indent}\t${this.generateComponentTag(comp)}`);
			});
			rows.push(`${indent}</cl-row>`);
		}

		// 第二行：表格
		if (this.hasTable()) {
			rows.push(`${indent}<cl-row>`);
			rows.push(`${indent}\t${this.generateTableTag()}`);
			rows.push(`${indent}</cl-row>`);
		}

		// 第三行：分页
		if (this.hasPagination()) {
			rows.push(`${indent}<cl-row>`);
			rows.push(`${indent}\t<cl-flex1 />`);
			rows.push(`${indent}\t<cl-pagination />`);
			rows.push(`${indent}</cl-row>`);
		}

		return rows.join('\n');
	}

	/**
	 * 获取第一行组件
	 */
	private getFirstRowComponents(): CrudComponentInstance[] {
		return this.page.components.filter(comp =>
			[
				'cl-refresh-btn',
				'cl-add-btn',
				'cl-multi-delete-btn',
				'cl-search-key',
				'cl-adv-btn',
				'cl-filter',
				'cl-import-btn',
				'cl-export-btn',
				'cl-flex1'
			].includes(comp.componentId)
		);
	}

	/**
	 * 生成组件标签
	 */
	private generateComponentTag(component: CrudComponentInstance): string {
		const props = this.generateComponentProps(component);

		if (component.children && component.children.length > 0) {
			const childrenContent = component.children
				.map(child => `\t\t${this.generateComponentTag(child)}`)
				.join('\n');
			return `<${component.componentId}${props}>\n${childrenContent}\n\t</${component.componentId}>`;
		} else {
			return `<${component.componentId}${props} />`;
		}
	}

	/**
	 * 生成组件属性
	 */
	private generateComponentProps(component: CrudComponentInstance): string {
		const props: string[] = [];

		Object.entries(component.props).forEach(([key, value]) => {
			if (value !== undefined && value !== null && value !== '') {
				if (typeof value === 'string') {
					props.push(`${key}="${value}"`);
				} else if (typeof value === 'boolean') {
					if (value) {
						props.push(key);
					}
				} else if (typeof value === 'number') {
					props.push(`:${key}="${value}"`);
				} else {
					props.push(`:${key}="${JSON.stringify(value)}"`);
				}
			}
		});

		return props.length > 0 ? ' ' + props.join(' ') : '';
	}

	/**
	 * 生成表格标签
	 */
	private generateTableTag(): string {
		const props: string[] = ['ref="Table"'];

		if (this.tableConfig.showSummary) {
			props.push('show-summary');
			if (this.tableConfig.summaryMethod) {
				props.push(`:summary-method="${this.tableConfig.summaryMethod}"`);
			}
		}

		if (this.tableConfig.autoHeight === false) {
			props.push(':auto-height="false"');
		}

		return `<cl-table ${props.join(' ')}>
			${this.generateTableSlots()}
		</cl-table>`;
	}

	/**
	 * 生成表格插槽
	 */
	private generateTableSlots(): string {
		const slots: string[] = [];

		if (this.tableConfig.columns) {
			this.tableConfig.columns.forEach((column: TableColumn) => {
				if (column.type === 'expand') {
					slots.push(this.generateExpandSlot());
				}
				// 可以添加更多自定义列插槽
			});
		}

		return slots.join('\n\t\t\t');
	}

	/**
	 * 生成展开插槽
	 */
	private generateExpandSlot(): string {
		return `<!-- 展开信息 -->
			<template #column-detail="{ scope }">
				<div style="padding: 0 10px">
					<el-descriptions border :column="3">
						<el-descriptions-item label="ID">
							{{ scope.row.id }}
						</el-descriptions-item>
						<!-- 更多字段... -->
					</el-descriptions>
				</div>
			</template>`;
	}

	/**
	 * 检查是否有表格
	 */
	private hasTable(): boolean {
		return this.page.components.some(comp => comp.componentId === 'cl-table');
	}

	/**
	 * 检查是否有分页
	 */
	private hasPagination(): boolean {
		return this.page.components.some(comp => comp.componentId === 'cl-pagination');
	}

	/**
	 * 检查是否有高级搜索
	 */
	private hasAdvSearch(): boolean {
		return this.page.components.some(comp => comp.componentId === 'cl-adv-search');
	}

	/**
	 * 生成脚本部分
	 */
	private generateScript(): string {
		const imports = this.generateImports();
		const composables = this.generateComposables();
		const configs = this.generateConfigs();
		const methods = this.generateMethods();
		const lifecycle = this.generateLifecycle();

		return `${imports}

${composables}

${configs}

${methods}

${lifecycle}`;
	}

	/**
	 * 生成导入语句
	 */
	private generateImports(): string {
		const imports = [
			"import { useCrud, useTable, useUpsert } from '@cool-vue/crud';",
			"import { ref } from 'vue';",
			"import { useDict } from '/$/dict';",
			"import { useCool } from '/@/cool';"
		];

		if (this.hasAdvSearch()) {
			imports[0] =
				"import { useCrud, useTable, useUpsert, useAdvSearch } from '@cool-vue/crud';";
		}

		if (this.shouldWrapInDialog()) {
			// 对话框模式不需要额外导入
		} else {
			imports.push("import { ElMessage } from 'element-plus';");
		}

		return imports.join('\n');
	}

	/**
	 * 生成组合式API
	 */
	private generateComposables(): string {
		const lines = ['const { service } = useCool();', 'const { dict } = useDict();'];

		if (this.shouldWrapInDialog()) {
			lines.push('const visible = ref(false);');
		}

		return lines.join('\n');
	}

	/**
	 * 生成配置
	 */
	private generateConfigs(): string {
		const configs: string[] = [];

		configs.push(this.generateCrudConfig());
		configs.push(this.generateTableConfig());
		configs.push(this.generateUpsertConfig());

		if (this.hasAdvSearch()) {
			configs.push(this.generateAdvSearchConfig());
		}

		return configs.join('\n\n');
	}

	/**
	 * 生成CRUD配置
	 */
	private generateCrudConfig(): string {
		const service = this.crudConfig.service || 'test';
		const hasEvents = this.crudConfig.onRefresh || this.crudConfig.onDelete;
		const hasDict = this.crudConfig.dict;

		let config = `// cl-crud 配置
const Crud = useCrud(
	{
		service: '${service}'`;

		if (hasDict) {
			config += `,

		dict: ${JSON.stringify(this.crudConfig.dict, null, 2).replace(/\n/g, '\n\t\t')}`;
		}

		if (this.crudConfig.onRefresh) {
			config += `,

		// 刷新事件
		onRefresh(params, { next }) {
			${this.crudConfig.onRefresh}
		}`;
		}

		if (this.crudConfig.onDelete) {
			config += `,

		// 删除事件
		onDelete(selection, { next }) {
			${this.crudConfig.onDelete}
		}`;
		}

		config += `
	},
	app => {
		app.refresh({
			size: 10
		});
	}
);`;

		return config;
	}

	/**
	 * 生成表格配置
	 */
	private generateTableConfig(): string {
		const columns = this.tableConfig.columns || [];
		const autoHeight =
			this.tableConfig.autoHeight !== undefined ? this.tableConfig.autoHeight : false;
		const contextMenu = this.tableConfig.contextMenu || ['refresh'];

		let config = `// cl-table 配置
const Table = useTable({
	autoHeight: ${autoHeight},
	contextMenu: ${JSON.stringify(contextMenu)},

	columns: [`;

		columns.forEach((column: TableColumn, index: number) => {
			config += '\n\t\t' + this.generateColumnConfig(column);
			if (index < columns.length - 1) {
				config += ',';
			}
		});

		config += `
	]
});`;

		return config;
	}

	/**
	 * 生成列配置
	 */
	private generateColumnConfig(column: TableColumn): string {
		const props: string[] = [];

		if (column.type) {
			props.push(`type: '${column.type}'`);
		}
		if (column.label) {
			props.push(`label: '${column.label}'`);
		}
		if (column.prop) {
			props.push(`prop: '${column.prop}'`);
		}
		if (column.width) {
			props.push(`width: ${column.width}`);
		}
		if (column.minWidth) {
			props.push(`minWidth: ${column.minWidth}`);
		}
		if (column.sortable) {
			props.push(`sortable: '${column.sortable}'`);
		}
		if (column.buttons) {
			props.push(`buttons: ${JSON.stringify(column.buttons)}`);
		}
		if (column.dict) {
			props.push(`dict: dict.get('${column.dict}')`);
		}
		if (column.component) {
			props.push(`component: ${JSON.stringify(column.component)}`);
		}

		return `{
			${props.join(',\n\t\t\t')}
		}`;
	}

	/**
	 * 生成Upsert配置
	 */
	private generateUpsertConfig(): string {
		const items = this.upsertConfig.items || [];

		let config = `// cl-upsert 配置
const Upsert = useUpsert({
	items: [`;

		items.forEach((item: FormItem, index: number) => {
			config += '\n\t\t' + this.generateFormItemConfig(item);
			if (index < items.length - 1) {
				config += ',';
			}
		});

		config += `
	]`;

		// 添加钩子函数
		if (this.upsertConfig.onInfo) {
			config += `,

	// 详情钩子
	onInfo(data, { next, done }) {
		${this.upsertConfig.onInfo}
	}`;
		}

		if (this.upsertConfig.onSubmit) {
			config += `,

	// 提交钩子
	onSubmit(data, { next, close, done }) {
		${this.upsertConfig.onSubmit}
	}`;
		}

		if (this.upsertConfig.onOpened) {
			config += `,

	// 打开后钩子
	onOpened(data) {
		${this.upsertConfig.onOpened}
	}`;
		}

		if (this.upsertConfig.onClose) {
			config += `,

	// 关闭钩子
	onClose(action, done) {
		${this.upsertConfig.onClose}
	}`;
		}

		config += `
});`;

		return config;
	}

	/**
	 * 生成表单项配置
	 */
	private generateFormItemConfig(item: FormItem): string {
		const props: string[] = [];

		if (item.label) {
			props.push(`label: '${item.label}'`);
		}
		if (item.prop) {
			props.push(`prop: '${item.prop}'`);
		}
		if (item.required) {
			props.push(`required: ${item.required}`);
		}
		if (item.component) {
			props.push(`component: ${JSON.stringify(item.component)}`);
		}
		if (item.group) {
			props.push(`group: '${item.group}'`);
		}
		if (item.span) {
			props.push(`span: ${item.span}`);
		}

		return `{
			${props.join(',\n\t\t\t')}
		}`;
	}

	/**
	 * 生成高级搜索配置
	 */
	private generateAdvSearchConfig(): string {
		const items = this.advSearchConfig.items || [];

		let config = `// cl-adv-search 配置
const AdvSearch = useAdvSearch({
	items: [`;

		items.forEach((item: FormItem, index: number) => {
			config += '\n\t\t' + this.generateFormItemConfig(item);
			if (index < items.length - 1) {
				config += ',';
			}
		});

		config += `
	]
});`;

		return config;
	}

	/**
	 * 生成方法
	 */
	private generateMethods(): string {
		const methods: string[] = [];

		if (this.shouldWrapInDialog()) {
			methods.push(`function open() {
	visible.value = true;
}`);
		}

		// 添加刷新方法
		methods.push(`// 刷新列表
function refresh(params?: any) {
	Crud.value?.refresh(params);
}`);

		return methods.join('\n\n');
	}

	/**
	 * 生成生命周期
	 */
	private generateLifecycle(): string {
		if (this.shouldWrapInDialog()) {
			return '';
		}

		return `// 页面加载时自动刷新
onMounted(() => {
	refresh();
});`;
	}

	/**
	 * 生成样式部分
	 */
	private generateStyle(): string {
		if (this.shouldWrapInDialog()) {
			return `.scope {
	.h {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 16px;
	}

	.c {
		margin-bottom: 16px;
	}

	.f {
		display: flex;
		justify-content: flex-end;
		color: #999;
		font-size: 12px;
	}
}`;
		}

		return `.crud-page {
	padding: 20px;
}`;
	}
}
