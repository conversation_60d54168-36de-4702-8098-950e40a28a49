import { type ModuleConfig } from '/@/cool';
export default (): ModuleConfig => {
	return {
		// 页面配置
		pages: [
			{
				name: 'lowcode-guide',
				path: '/lowcode/guide',
				component: () => import('./views/guide.vue'),
				meta: {
					label: '使用指南'
				}
			},
			{
				name: 'lowcode-demo',
				path: '/lowcode/demo',
				component: () => import('./views/demo.vue'),
				meta: {
					label: '功能演示'
				}
			},
			{
				name: 'lowcode-builder',
				path: '/lowcode/builder',
				component: () => import('./views/builder.vue'),
				meta: {
					label: '页面构建器'
				}
			},
			{
				name: 'lowcode-pages',
				path: '/lowcode/pages',
				component: () => import('./views/pages.vue'),
				meta: {
					label: '页面管理'
				}
			},
			{
				name: 'lowcode-components',
				path: '/lowcode/components',
				component: () => import('./views/components.vue'),
				meta: {
					label: '组件库'
				}
			},
			{
				name: 'lowcode-test',
				path: '/lowcode/test',
				component: () => import('./views/test-generated.vue'),
				meta: {
					label: '代码测试'
				}
			}
		]
	};
};
